function fillMultipleContracts() {
    // Отримуємо активну електронну таблицю
    const ss = SpreadsheetApp.getActiveSpreadsheet();

    // Отримуємо аркуші "Реєстр договорів" і "Контрагенти"
    const contractsSheet = ss.getSheetByName("Реєстр договорів");
    const contractorsSheet = ss.getSheetByName("Контрагенти");

    // Отримуємо дані з аркуша "Реєстр договорів"
    const contractsData = contractsSheet.getDataRange().getValues();

    // Знаходимо індекс колонки статусу за назвою
    const statusColumnIndex = findColumnIndexByName(contractsSheet, "Статус");
     // Знаходимо індекс колонки номеру договору за назвою
    const contractNumberColumnIndex = findColumnIndexByName(contractsSheet, "№ договору");

    // Якщо колонку "Статус" не знайдено, показуємо помилку і завершуємо виконання
    if (statusColumnIndex === -1) {
      SpreadsheetApp.getUi().alert("Колонка 'Статус' не знайдена в аркуші 'Реєстр договорів'.");
      return;
    }
   // Якщо колонку "Номер договору" не знайдено, показуємо помилку і завершуємо виконання
    if (contractNumberColumnIndex === -1) {
      SpreadsheetApp.getUi().alert("Колонка '№ договору' не знайдена в аркуші 'Реєстр договорів'.");
      return;
    }
    
    // Змінна для зберігання кількості оброблених договорів
    let processedContracts = 0;

    // Ітеруємо по кожному рядку в "Реєстрі договорів", починаючи з другого (заголовки пропускаємо)
    for (let i = 1; i < contractsData.length; i++) {
        const contractRow = contractsData[i];
        const status = contractRow[statusColumnIndex];

      // Перевіряємо, чи статус "Не підписаний"
      if (status === 'Не підписаний') {
        // Отримуємо дані з поточного рядка
        const contractData = getRowData(contractsSheet, i + 1); // i+1 тому що рядки рахуються з 1

        // Скорочена назва контрагента для пошуку
        const contractorShortName = contractData[0];
        // Номер договору
        const contractNumber = contractData[contractNumberColumnIndex];
        
        // Знаходимо дані контрагента
        const contractorInfo = findContractor(contractorsSheet, contractorShortName);
        
        if (!contractorInfo) {
          // Якщо контрагента не знайдено, виводимо повідомлення (не зупиняємо виконання)
          Logger.log(`Контрагент з назвою "${contractorShortName}" не знайдений у аркуші "Контрагенти". Рядок ${i + 1}`);
          continue; // Переходимо до наступного рядка
        }

        // Вибір ID шаблону договору
        const templateDocId = getTemplateDocId(contractorInfo);
      
        // ID папки, куди буде збережена копія документа
        const folderId = '1-uuUfuuqmMYXByZRONxenQx7UxDjYWKk';
    
        // Створюємо копію шаблону документа і отримуємо її ID
        const newDocId = createDocumentCopy(templateDocId, folderId, contractorShortName, contractNumber);

        // Відкриваємо створену копію документа для заповнення
        const newDoc = DocumentApp.openById(newDocId);
        const newBody = newDoc.getBody();
    
        // Підготовка плейсхолдерів для заміни та їх значень
        const placeholders = {
          '{{contractor}}': contractorInfo[1],
          '{{edrpou}}': contractorInfo[2],
          '{{IPN}}': contractorInfo[3],
          '{{contractDate}}': formatContractDate(contractData[1]),
          '{{contractNumber}}': contractData[3],
          '{{representative}}': contractorInfo[4],
          '{{signatoryName}}': contractorInfo[5],
          '{{signatoryInitials}}': formatSignatoryInitials(contractorInfo[5]),
          '{{legalAddress}}': contractorInfo[6],
          '{{bankAccount}}': contractorInfo[7],
          '{{bankMfo}}': contractorInfo[8],
          '{{bank}}': contractorInfo[9],
          '{{phone}}': contractorInfo[10],
          '{{email}}': contractorInfo[11]
        };
    
        // Заміна плейсхолдерів у документі
        const emptyPlaceholders = replacePlaceholders(newBody, placeholders);
    
        // Зберігаємо і закриваємо документ
        newDoc.saveAndClose();

        // Якщо є пусті дані, виводимо повідомлення (не зупиняємо виконання)
        if (emptyPlaceholders.length > 0) {
          Logger.log(`Деякі дані залишились пустими у рядку ${i + 1}: ${emptyPlaceholders.join(', ')}. Перевірте інформацію в таблицях.`);
        }

          // Змінюємо статус на "Відправлено"
        contractsSheet.getRange(i+1, statusColumnIndex + 1).setValue("Відправлений");
        
        //Збільшуємо лічильник оброблених договорів
        processedContracts++;
        // Щоб уникнути ліміту часу на виконання
        SpreadsheetApp.flush(); // Важливо для фіксації змін в таблиці
        // Utilities.sleep(500);    // Пауза між обробками
      }
    }
      // Останнє повідомлення, скільки договорів оброблено
      if (processedContracts > 0) {
        SpreadsheetApp.getUi().alert(`Створено ${processedContracts} договори.`);
      } else{
        SpreadsheetApp.getUi().alert(`Не знайдено договорів зі статусом "Не підписаний".`);
      }
}

/**
 * Знаходить індекс колонки за її назвою.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet Аркуш
 * @param {string} columnName Назва колонки
 * @return {number} Індекс колонки (починаючи з 0), або -1, якщо не знайдено
 */
function findColumnIndexByName(sheet, columnName) {
  const headerRow = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
  return headerRow.indexOf(columnName);
}

// ------------------- Допоміжні функції (модифіковані) -------------------

/**
 * Створює копію шаблону документа у вказаній папці.
 * @param {string} templateDocId ID шаблону
 * @param {string} folderId ID папки
  * @param {string} contractorShortName Скорочена назва контрагента
  *  @param {string} contractNumber Номер договору
 * @return {string} ID новоствореного документа
 */
function createDocumentCopy(templateDocId, folderId, contractorShortName, contractNumber) {
  const folder = DriveApp.getFolderById(folderId);
  const contractName = `${contractorShortName} - Договір №${contractNumber}`;
  return DriveApp.getFileById(templateDocId).makeCopy(contractName, folder).getId();
}

/**
 * Отримує активний рядок з аркуша.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet Аркуш
 * @return {number} Номер активного рядка
 */
function getSelectedRow(sheet) {
  return sheet.getActiveCell().getRow();
}

/**
 * Отримує дані з рядка.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet Аркуш
 * @param {number} row Номер рядка
 * @return {Array} Масив даних рядка
 */
function getRowData(sheet, row) {
  return sheet.getRange(row, 1, 1, sheet.getLastColumn()).getValues()[0];
}

/**
 * Шукає контрагента за скороченою назвою.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet Аркуш "Контрагенти"
 * @param {string} contractorShortName Скорочена назва контрагента
 * @return {Array|null} Дані контрагента або null, якщо не знайдено
 */
function findContractor(sheet, contractorShortName) {
  const contractorsData = sheet.getRange(2, 1, sheet.getLastRow() - 1, sheet.getLastColumn()).getValues();
  return contractorsData.find(row => row[0] === contractorShortName);
}

/**
 * Отримує ID шаблону документа залежно від типу контрагента.
 * @param {Array} contractorInfo Дані контрагента
 * @return {string} ID шаблону
 */
function getTemplateDocId(contractorInfo) {
  return contractorInfo[4] === 'Фіз. особа - підприємець'
    ? '1pzKXlh_okax-LH0i6beA1xUhputsSXNJCsoxsuUGnIg'
    : '19WbmrHqNOeQWnENtRZeNlg3SvMTAEfxH9ja1JGTspeA';
}

/**
 * Форматує дату договору у вигляді "день місяця рік".
 * @param {Date|string} date Дата
 * @return {string} Форматована дата
 */
function formatContractDate(date) {
  if (!(date instanceof Date)) return date;
  const months = [
    'січня', 'лютого', 'березня', 'квітня', 'травня', 'червня',
    'липня', 'серпня', 'вересня', 'жовтня', 'листопада', 'грудня'
  ];
  const day = Utilities.formatDate(date, Session.getScriptTimeZone(), "d");
  const year = Utilities.formatDate(date, Session.getScriptTimeZone(), "yyyy");
  return `${day} ${months[date.getMonth()]} ${year}`;
}

/**
 * Форматує підписанта у вигляді "Прізвище І.О.".
 * @param {string} fullName Повне ім'я
 * @return {string} Форматоване ім'я
 */
function formatSignatoryInitials(fullName) {
  if (!fullName) return '';
  const names = fullName.split(' ');
  const initials = names.slice(1).map(name => name[0] + '.').join('');
  return names[0] + ' ' + initials;
}

/**
 * Замінює плейсхолдери у документі на їх значення.
 * @param {GoogleAppsScript.Document.Body} body Тіло документа
 * @param {Object} placeholders Об'єкт плейсхолдерів
 * @return {Array} Список пустих плейсхолдерів
 */
function replacePlaceholders(body, placeholders) {
  const emptyKeys = [];
  for (const [placeholder, value] of Object.entries(placeholders)) {
    body.replaceText(placeholder, value || '');
    if (!value) {
      emptyKeys.push(placeholder);
    }
  }
  return emptyKeys;
}