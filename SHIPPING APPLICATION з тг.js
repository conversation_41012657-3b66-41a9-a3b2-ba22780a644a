const SHIPPING_FORM_ID = '1_5-1WbJsm_AQNMG6Nwnkn4yuShfQ_yXRztZw0OpHdlU';// ID форми
const SHIPPING_TEMPLATE_ID = '1qUW6M5kEW_53o-25QtRcjq_lraXFK1IZ8Jt9M6Q9P-c';// ID шаблону
const SHIPPING_FOLDER_ID = '1WjI5YyynvKQDYe0mHWty3QiDULtkPIIt';// ID папки
const SHIPPING_SHEET_ID = '1IsqVkWE0jGkfzNPiPYktxzIFCnDMF0i3IbgZcglQvt8';// ID таблиці
const SHIPPING_SHEET_NAME = 'SHIPPING APPLICATION';
const REGISTRY_SHEET_NAME = 'Реєстр водіїв і автомобілів';

const BOT_TOKEN_SHIPPING = '**********:AAHAsQ-2bVU8hxpeW7heWeAX7UTLa20yaHk';// Токен Marsik
const API_URL_SHIPPING = `https://api.telegram.org/bot${BOT_TOKEN_SHIPPING}/sendDocument`;
// const CHAT_ID_SHIPPING = '276789696';// чат зі мною
const CHAT_ID_SHIPPING = '-**********'; // chat_id групи

function createShippingApplication(responses, headers) {
    // Створити новий Google Doc з шаблону
    const sheet = SpreadsheetApp.openById(SHIPPING_SHEET_ID).getSheetByName(SHIPPING_SHEET_NAME);
    const lastRow = sheet.getLastRow();
    const doc = DriveApp.getFileById(SHIPPING_TEMPLATE_ID).makeCopy();
    const docId = doc.getId();
    const docBody = DocumentApp.openById(docId).getBody();
    // Перемістити документ в папку
    DriveApp.getFileById(docId).moveTo(DriveApp.getFolderById(SHIPPING_FOLDER_ID));
    // генеруємо номер документу попорядку за кількістю рядків в аркуші
    const docNumber = lastRow; // номер документу
    const driverName = responses[headers.indexOf('Водій')]; // ім'я водія
    // перейменовуємо документ
    const fileName = `SHIPPING APPLICATION №${docNumber} ${driverName}`;
    DriveApp.getFileById(docId).setName(fileName);

    // форматуємо дату
    const date_devery = formatDateTime_ddMMyyyy(responses[headers.indexOf('Дата доставки транспортних засобів')]);

    // Визначаємо митницю виїзду
    let customsExit;
    if (responses[headers.indexOf('Місце завантаження')] === "Terminal: AB «Klaipėdos nafta» Fabryko per.2, LT-68127, Mariyampole, Lytva") {
        customsExit = "Kaunas Territorial Customs, Marijampole Cargo Post";
    } else {
        customsExit = responses[headers.indexOf('Місце завантаження')];
    }

    // Отримуємо номери автомобіля і причепа з відповіді форми
    const vehicleNumbers = responses[headers.indexOf('Номер автомобіля і причепа')].split(' ');
    const carNumber = vehicleNumbers[0] || '';
    const trailerNumber = vehicleNumbers[1] || '';

    // Замінити заповнювачі відповідями з форми
    docBody.replaceText('{{Номер документу}}', docNumber);
    docBody.replaceText('{{Дата доставки транспортних засобів}}', date_devery);
    docBody.replaceText('{{Водій}}', driverName);
    docBody.replaceText('{{Місце завантаження}}', responses[headers.indexOf('Місце завантаження')]);
    docBody.replaceText('{{Митниця виїзду}}', customsExit);
    docBody.replaceText('{{Вид палива}}', responses[headers.indexOf('Вид палива')]);
    docBody.replaceText('{{Номер автомобіля}}', carNumber);
    docBody.replaceText('{{Номер причепа}}', trailerNumber);
    // Зберегти та закрити документ
    DocumentApp.openById(docId).saveAndClose();

    // Генеруємо PDF після створення документа
    const pdfBlob = DriveApp.getFileById(docId).getAs('application/pdf');
    const pdfFile = DriveApp.getFolderById(SHIPPING_FOLDER_ID).createFile(pdfBlob)
        .setName(`${fileName}.pdf`);

    // Відправляємо PDF в телеграм
    sendShippingDocumentToTelegram(pdfFile.getId(), CHAT_ID_SHIPPING);
}

// функція яка шукає номер автомобіля і причіпа в реєстрі за назвою водія
// function findCarNumber(driver) {
//     const sheet = SpreadsheetApp.openById(SHIPPING_SHEET_ID).getSheetByName(REGISTRY_SHEET_NAME);
//     const data = sheet.getDataRange().getValues();
//     const headers = data[0];
    
//     const driverIndex = headers.indexOf('І.П. Англійською');
//     const carNumberIndex = headers.indexOf('№ Автомобіля');
//     const trailerNumberIndex = headers.indexOf('№ причепа');
    
//     if (driverIndex === -1 || carNumberIndex === -1 || trailerNumberIndex === -1) {
//         return { carNumber: 'Не вдалося знайти необхідні стовпці', trailerNumber: '' };
//     }
    
//     for (let i = 1; i < data.length; i++) {
//         if (data[i][driverIndex] === driver) {
//             return { carNumber: data[i][carNumberIndex], trailerNumber: data[i][trailerNumberIndex] };
//         }
//     }
//     return { carNumber: 'Номер автомобіля не знайдено', trailerNumber: '' };
// }

// Додаємо функцію для відправки документу в Telegram
function sendShippingDocumentToTelegram(documentId, chatId) {
    if (!documentId || !chatId) {
        Logger.log('Missing parameters for sending document');
        return;
    }

    Logger.log(`Відправка документу: ${documentId} до чату: ${chatId}`);
    
    const documentBlob = DriveApp.getFileById(documentId).getBlob();
    const payload = {
        chat_id: chatId,
        document: documentBlob
    };
    
    const options = {
        method: 'post',
        payload: payload,
        muteHttpExceptions: true
    };
    
    try {
        const response = UrlFetchApp.fetch(API_URL_SHIPPING, options);
        Logger.log('Документ відправлено успішно');
    } catch (error) {
        Logger.log('Error sending document: ' + error.message);
    }
}