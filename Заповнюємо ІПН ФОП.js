function fillIPNForIndividuals() {
  // Отримати активний лист
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName("Контрагенти")
  
  // Отримати всі дані з таблиці
  const data = sheet.getDataRange().getValues();
  
  // Пройтися по рядках (починаючи з другого, бо перший зазвичай заголовок)
  for (let i = 1; i < data.length; i++) {
    const edrpou = data[i][2]; // Стовпець C (ЄДРПОУ) - індекс 2
    const ipn = data[i][3];   // Стовпець D (ІПН) - індекс 3
    const personType = data[i][4]; // Стовпець E (в особі) - індекс 4

    // Якщо значення в стовпці E "Фіз. особа - підприємець" і ІПН порожній
    if (personType === "Фіз. особа - підприємець" && ipn === "") {
      // Копіювати значення з ЄДРПОУ в ІПН
      sheet.getRange(i + 1, 4).setValue(edrpou); // i+1 бо нумерація з 1, а цикл з 0
    }
  }
}
