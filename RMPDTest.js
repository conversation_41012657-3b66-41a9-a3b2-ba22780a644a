// --- Глобальні константи (можна винести налаштування середовища сюди) ---
const IS_TEST_ENVIRONMENT = true; // Встановіть true для тестування, false для продуктиву
const TEST_CARRIER_EMAIL = '<EMAIL>'; // Ваш реальний email перевізника

// Функція, яку ви будете запускати для тестування генерації XML
function generateTestRMPDFile() {
    Logger.log("Початок генерації тестового RMPD XML...");

    // --- Тестові дані для запуску (замініть на актуальні, якщо потрібно для логіки) ---
    const testData = {
        deliveryDate: new Date(), // Поточна дата для прикладу
        vehicleTrailerCombined: "BH0001XX CE0002XX", // Приклад номера авто і причепа
        borderCrossingPoint: "Dorohusk - Jogodzin", // Приклад пункту перетину
        // Імітуємо дані, які б повернула функція findVehicleInfo
        vehicleInfo: {
            truckNumber: "BH0001XX",
            trailerNumber: "CE0002XX",
            // Реальні GPS, які потім будуть перетворені на тестові, якщо IS_TEST_ENVIRONMENT = true
            geoLocator: "Z24-ACTUAL-GPS-1",
            geoLocator2: "M24-ACTUAL-GPS-2"
        }
    };

    // --- Логіка, схожа на createRMPD100, але адаптована для тестового запуску ---
    const formattedStartDate = formatDate_yyyyMMdd(testData.deliveryDate);
    const endDateRaw = new Date(new Date(testData.deliveryDate).getTime() + 10 * 24 * 60 * 60 * 1000);
    const formattedEndDate = formatDate_yyyyMMdd(endDateRaw);

    if (!testData.vehicleInfo) {
        Logger.log(`Помилка: Тестові дані для vehicleInfo не надані.`);
        return;
    }

    const xmlContent = generateXMLtest({
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        vehicleInfo: testData.vehicleInfo, // Передаємо імітовані дані про ТЗ
        borderCrossing: testData.borderCrossingPoint
    }, IS_TEST_ENVIRONMENT); // Передаємо флаг тестового середовища

    if (xmlContent) {
        Logger.log("--- Згенерований RMPD XML ---");
        Logger.log(xmlContent);
        Logger.log("-----------------------------");

        // Зберігаємо XML у файл на Google Drive для зручності
        try {
            const fileName = `TestRMPD100_Timestamp_${new Date().getTime()}.xml`;
            DriveApp.createFile(fileName, xmlContent, 'application/xml');
            Logger.log(`Тестовий XML файл "${fileName}" успішно створено в кореневій папці Google Drive.`);
        } catch (e) {
            Logger.log(`Помилка при збереженні файлу на Google Drive: ${e.toString()}`);
        }
    } else {
        Logger.log("Помилка: XML контент не було згенеровано.");
    }
}


function formatDate_yyyyMMdd(date) {
    if (!date) return '';
    try {
        const d = new Date(date);
        if (isNaN(d.getTime())) {
             Logger.log("Недійсна дата передана в formatDate_yyyyMMdd: " + date);
             const parts = date.toString().match(/(\d+)/g);
             if (parts && parts.length >= 3) {
                 const day = parts[0]; const month = parts[1]; const year = parts[2];
                 if (year.length === 4 && month.length <=2 && day.length <=2) {
                     return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                 }
             }
             return date.toString();
        }
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    } catch (e) {
        Logger.log("Помилка форматування дати: " + date + ". Помилка: " + e);
        return date.toString();
    }
}

// Допоміжна функція для генерації випадкових даних для GPS відповідно до шаблону
function generateRandomGpsPart(length, charSet) {
    let result = '';
    const charSetArray = charSet.split(''); // Розділити на масив символів
    for (let i = 0; i < length; i++) {
        result += charSetArray[Math.floor(Math.random() * charSetArray.length)];
    }
    return result;
}

function generateXMLtest(data, isTestEnv = false) {
    let routePlaceEntrance = data.borderCrossing || "Hrebenne - Rawa Ruska";
    let routeNumberEntrance = "DK17";
    const normalizedBorderCrossing = routePlaceEntrance.toLowerCase().replace(/\s*-\s*/, '-');

    if (normalizedBorderCrossing === "dorohusk-jogodzin") {
        routeNumberEntrance = "DK12";
    } else if (normalizedBorderCrossing === "hrebenne-rawa ruska") {
        routeNumberEntrance = "DK17";
    } else if (normalizedBorderCrossing === "zosin-ustiług") {
        routeNumberEntrance = "S74";
    } else {
        Logger.log(`Невідомий пункт перетину кордону: ${data.borderCrossing}. Використовується значення за замовчуванням: Hrebenne - Rawa Ruska, DK17.`);
        routePlaceEntrance = "Hrebenne - Rawa Ruska";
        routeNumberEntrance = "DK17";
    }

    let finalGeoLocatorNumber = data.vehicleInfo.geoLocator || "";
    let finalFailoverGeoLocatorNumber = data.vehicleInfo.geoLocator2 || "";

    if (isTestEnv) {
        Logger.log("Режим ТЕСТОВОГО СЕРЕДОВИЩА: генерація тестових GPS номерів за ТОЧНИМ шаблоном.");
        
        // Літери з шаблону: [A,B,C,E,F,G,H,K,M,N,P,R,S,T,W,X,Y,Z]
        const allowedLetters = "ABCEFGH KMNPRSTWXYZ".replace(/\s/g, ''); // Видаляємо пробіл, якщо він там був для читабельності
        const digits = "0123456789";

        // Функція для генерації одного тестового GPS номера
        const createTestGpsNumber = (startLetterPrefixPool) => {
            const prefix = startLetterPrefixPool[Math.floor(Math.random() * startLetterPrefixPool.length)]; // Вибираємо випадковий префікс типу Z00, M01
            
            const part1_letters = generateRandomGpsPart(2, allowedLetters); // ДВІ ЛІТЕРИ
            const part2_digits = generateRandomGpsPart(2, digits);          // ДВІ ЦИФРИ
            const part3_letters = generateRandomGpsPart(2, allowedLetters); // ДВІ ЛІТЕРИ
            const part4_digit = generateRandomGpsPart(1, digits);           // ОДНА ЦИФРА
            
            return `${prefix}-${part1_letters}${part2_digits}${part3_letters}-${part4_digit}`;
        };
        
        const testPrefixes = ['Z00', 'U00', 'M00', 'Z01', 'U01', 'M01'];

        finalGeoLocatorNumber = createTestGpsNumber(testPrefixes);
        
        if (data.vehicleInfo.geoLocator2 || (data.vehicleInfo.trailerNumber && data.vehicleInfo.trailerNumber !== "")) {
             finalFailoverGeoLocatorNumber = createTestGpsNumber(testPrefixes);
             while (finalFailoverGeoLocatorNumber === finalGeoLocatorNumber) { // Щоб уникнути однакових
                 finalFailoverGeoLocatorNumber = createTestGpsNumber(testPrefixes);
             }
        } else {
            finalFailoverGeoLocatorNumber = ""; 
        }
        
        Logger.log(`Згенеровані тестові GPS: Основний = ${finalGeoLocatorNumber}, Запасний = ${finalFailoverGeoLocatorNumber}`);
    } else {
        Logger.log("Режим ПРОДУКЦІЙНОГО СЕРЕДОВИЩА: використовуються реальні GPS номери.");
    }

    // Ваш CARRIER_EMAIL, а не TEST_CARRIER_EMAIL, має бути в XML, якщо це вимога
    const carrierEmailForXml = TEST_CARRIER_EMAIL; // Або CARRIER_EMAIL, залежно від того, що потрібно в XML

    return `<?xml version="1.0" encoding="UTF-8"?>
<p:RMPD_100 xmlns:p="http://www.mf.gov.pl/RMPD/2024/03/18/RMPD_100.xsd"
            xmlns:tp="http://www.mf.gov.pl/RMPD/2024/03/18/PTypes.xsd">
    <p:Carrier>
        <tp:TraderInfo>
            <tp:IdSisc>FRUA1215779550001</tp:IdSisc>
            <tp:TraderName>MAGISTR TRANS LLC</tp:TraderName>
            <tp:TraderIdentityType>INNY</tp:TraderIdentityType>
            <tp:TraderIdentityNumber>UA45561434</tp:TraderIdentityNumber>
        </tp:TraderInfo>
        <tp:TraderAddress>
            <tp:Street>9 RUE str. Promyslova</tp:Street>
            <tp:HouseNumber>BRAK</tp:HouseNumber>
            <tp:City>Plotycha village</tp:City>
            <tp:Country>UA</tp:Country>
            <tp:PostalCode>47704</tp:PostalCode>
        </tp:TraderAddress>
    </p:Carrier>
    <p:PermissionInfo>
        <tp:StartTransportDate>${data.startDate}</tp:StartTransportDate>
        <tp:EndTransportDate>${data.endDate}</tp:EndTransportDate>
        <tp:CountryLoadCode>UA</tp:CountryLoadCode>
        <tp:CountryUnloadCode>LT</tp:CountryUnloadCode>
        <tp:Loaded>false</tp:Loaded>
        <tp:TypeOfRoadTransport>TRANSIT_TRANSPORT</tp:TypeOfRoadTransport>
        <tp:TypeOfPermission>NOT_OBLIGED</tp:TypeOfPermission>
        <tp:NotObligedLegalBase>094</tp:NotObligedLegalBase>
        <tp:JourneyDirection>1</tp:JourneyDirection>
        <tp:StartEndPlaceJourney>
            <tp:EntranceToPoland>
                <tp:RoutePlace>${routePlaceEntrance}</tp:RoutePlace>
                <tp:RouteNumber>${routeNumberEntrance}</tp:RouteNumber>
            </tp:EntranceToPoland>
            <tp:ExitFromPoland>
                <tp:RoutePlace>Budzisko - Kalwaria</tp:RoutePlace>
                <tp:RouteNumber>DK8</tp:RouteNumber>
            </tp:ExitFromPoland>
            <tp:EndInsidePL>
                <tp:Street>Testowa</tp:Street>
                <tp:HouseNumber>10</tp:HouseNumber>
                <tp:City>Warszawa</tp:City>
                <tp:Country>PL</tp:Country>
                <tp:PostalCode>00-001</tp:PostalCode>
                <tp:CodeTERC>1465011</tp:CodeTERC>
                <tp:Latitude>52.2297</tp:Latitude>
                <tp:Longitude>21.0122</tp:Longitude>
            </tp:EndInsidePL>
        </tp:StartEndPlaceJourney>
    </p:PermissionInfo>
    <p:MeansOfTransport>
        <tp:TruckCountry>UA</tp:TruckCountry>
        <tp:TruckNumber>${data.vehicleInfo.truckNumber}</tp:TruckNumber>
        <tp:TrailerCountry>UA</tp:TrailerCountry>
        <tp:TrailerNumber>${data.vehicleInfo.trailerNumber}</tp:TrailerNumber>
        <tp:GeoLocatorNumber>${finalGeoLocatorNumber}</tp:GeoLocatorNumber>
        <tp:FailoverGeoLocatorNumber>${finalFailoverGeoLocatorNumber}</tp:FailoverGeoLocatorNumber>
        <tp:FailoverCarrierEmail>${carrierEmailForXml}</tp:FailoverCarrierEmail> 
    </p:MeansOfTransport>
    <p:ResponseAddress>
        <tp:EmailChannel>
            <tp:EmailAddress1><EMAIL></tp:EmailAddress1> 
            <tp:EmailAddress2><EMAIL></tp:EmailAddress2>
        </tp:EmailChannel>
    </p:ResponseAddress>
    <p:Statements>
        <tp:Statement1>true</tp:Statement1>
        <tp:FirstName>Taras</tp:FirstName>
        <tp:LastName>Shkvarok</tp:LastName>
    </p:Statements>
</p:RMPD_100>`;
}
