function importHTMLDataByName() {
  const fileName = "контрагенти.htm";  // Замість YOUR_FILE_NAME вставте точну назву файлу
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();

  // Шукаємо файл за назвою
  const files = DriveApp.getFilesByName(fileName);
  
  // // Перевірка наявності файлів з такою назвою
  // if (!files.hasNext()) {
  //   SpreadsheetApp.getUi().alert("Файл з назвою " + fileName + " не знайдено.");
  //   return;
  // }

  // Отримуємо перший знайдений файл
  const file = files.next();
  const data = file.getBlob().getDataAsString();

  // Використовуємо регулярний вираз для пошуку всіх вмістів тегів <TD>
  const tdMatches = [...data.matchAll(/<td[^>]*>(.*?)<\/td>/gi)];
  
  // Проходимося по знайдених значеннях і вставляємо їх в Google Таблицю
  let row = 1;
  let col = 1;
  
  tdMatches.forEach(match => {
    let cellText = match[1].trim();

    // Обрізаємо текст до 5000 символів, якщо він довший
    if (cellText.length > 5000) {
      cellText = cellText.substring(0, 5000);
    }
    
    // Вставляємо текст у таблицю
    sheet.getRange(row, col).setValue(cellText);

    // Переходимо до наступної колонки або рядка
    col++;
    if (col > sheet.getMaxColumns()) {
      col = 1;
      row++;
    }
  });
}
