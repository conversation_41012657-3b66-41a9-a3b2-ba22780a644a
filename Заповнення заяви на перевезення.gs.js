function fillTemplate() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  const range = sheet.getActiveRange();
  const row = range.getRow();
  const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
  const data = sheet.getRange(row, 1, 1, sheet.getLastColumn()).getValues()[0];
  const rowData = {};

  headers.forEach((header, index) => {
    rowData[header] = data[index];
  });

  const ui = SpreadsheetApp.getUi();

  const fuelOptions = [
    "Паливо дизельне клас F",
    "Паливо дизельне клас C",
    "Паливо дизельне клас E",
    "Бензин автомобільний А-95 \"Premium\""
  ];

  const fuelResponse = ui.prompt(
    "Оберіть номер нафтопродукту з цього списку:\n1. Дизельне паливо F клас\n2. Дизельне паливо С клас\n3. Дизельне паливо E клас\n4. Бензин автомобільний А-95 \"Premium\"",
    ui.ButtonSet.OK_CANCEL
  );

  if (fuelResponse.getSelectedButton() !== ui.Button.OK) {
    ui.alert('Операцію скасовано.');
    return;
  }

  const fuelIndex = parseInt(fuelResponse.getResponseText().trim(), 10) - 1;
  if (isNaN(fuelIndex) || fuelIndex < 0 || fuelIndex >= fuelOptions.length) {
    ui.alert('Невірний вибір. Спробуйте ще раз.');
    return;
  }

  const fuelName = fuelOptions[fuelIndex];

  const dateResponse = ui.prompt('Введіть дату завантаження (ДД.ММ.РРРР):');
  if (dateResponse.getSelectedButton() !== ui.Button.OK) {
    ui.alert('Операцію скасовано.');
    return;
  }

  const loadDateInput = dateResponse.getResponseText().trim();
  const loadDatePattern = /^(\d{2})\.(\d{2})\.(\d{4})$/;
  const loadDateMatch = loadDateInput.match(loadDatePattern);

  if (!loadDateMatch) {
    ui.alert('Невірний формат дати. Введіть дату у форматі ДД.ММ.РРРР.');
    return;
  }

  const loadDate = `${loadDateMatch[1]}.${loadDateMatch[2]}.${loadDateMatch[3]}`;

  const volumeResponse = ui.prompt('Введіть кількість літрів для завантаження:');
  if (volumeResponse.getSelectedButton() !== ui.Button.OK) {
    ui.alert('Операцію скасовано.');
    return;
  }

  const loadVolume = parseFloat(volumeResponse.getResponseText().trim());
  if (isNaN(loadVolume) || loadVolume <= 0) {
    ui.alert('Невірне значення для кількості літрів. Спробуйте ще раз.');
    return;
  }

  const priceResponse = ui.prompt('Введіть погоджену ціну з ПДВ (грн/т):');
  if (priceResponse.getSelectedButton() !== ui.Button.OK) {
    ui.alert('Операцію скасовано.');
    return;
  }

  const agreedPrice = parseFloat(priceResponse.getResponseText().trim());
  if (isNaN(agreedPrice) || agreedPrice <= 0) {
    ui.alert('Невірне значення для ціни. Спробуйте ще раз.');
    return;
  }

  // Отримання секцій з таблиці
  const sections = [];
  for (let i = 1; i <= 11; i++) {
    const sectionVolume = parseFloat(rowData[`Сеція №${i} л.`]);
    if (sectionVolume) {
      sections.push({ id: i, volume: sectionVolume });
    }
  }

  // Функція для пошуку всіх комбінацій секцій
  function findCombination(sections, targetVolume) {
    const result = [];

    function helper(currentCombo, remainingSections, remainingVolume) {
      if (remainingVolume === 0) {
        result.push([...currentCombo]);
        return;
      }

      if (remainingVolume < 0 || remainingSections.length === 0) {
        return;
      }

      // Додаємо поточну секцію до комбінації
      const [currentSection, ...restSections] = remainingSections;
      helper([...currentCombo, currentSection], restSections, remainingVolume - currentSection.volume);

      // Пропускаємо поточну секцію
      helper(currentCombo, restSections, remainingVolume);
    }

    helper([], sections, targetVolume);
    return result;
  }

  const combinations = findCombination(sections, loadVolume);
  if (combinations.length === 0) {
    ui.alert('Неможливо завантажити точний обсяг. Задайте інший обсяг або змініть секції.');
    return;
  }

  // Вибираємо першу підходящу комбінацію
  const selectedCombination = combinations[0];
  const selectedSectionsInfo = selectedCombination.map(sec => `${sec.id} - ${sec.volume} л`).join(', ');

  const totalSectionsInfo = sections.map(sec => `${sec.id} - ${sec.volume} л`).join(', ');

  const sequenceNumber = parseInt(PropertiesService.getScriptProperties().getProperty('sequenceNumber') || 1, 10);
  PropertiesService.getScriptProperties().setProperty('sequenceNumber', (sequenceNumber + 1).toString());

  const templateId = '1kQzkbi_fGrEJgowAc05pokuEEcbKsUg6kix5oLPuooY';
  const template = DriveApp.getFileById(templateId);
  const folderId = '1WdJOXmovLz2zgbdMSJf7t_mNiGkmX_xz'; // ID папки для збереження
  const folder = DriveApp.getFolderById(folderId);

  const fileName = `Заявка №${sequenceNumber} - ${loadDate.replace(/\./g, '/')} - ${rowData['ПІП Водія']}`;
  const document = template.makeCopy(fileName, folder);
  const doc = DocumentApp.openById(document.getId());
  const body = doc.getBody();

  const placeholders = {
    '{{Вихідний номер}}': sequenceNumber,
    '{{Дата завантаження}}': loadDate,
    '{{Назва нафтопродукту}}': fuelName,
    '{{Об’єм}}': rowData["Загальний об'єм л."],
    '{{Кількість секцій}}': `${sections.length} (${totalSectionsInfo})`,
    '{{Секції}}': selectedSectionsInfo,
    '{{Кількість літрів}}': loadVolume,
    '{{Ціна}}': agreedPrice,
    '{{Водій}}': `${rowData['ПІП Водія']} тел. ${rowData['Телефон']}`,
    '{{Посвідчення водія}}': rowData['Посвідчення водія (серія, номер)'],
    '{{Паспорт}}': `${rowData['Паспорт серія']} виданий ${rowData['Ким видано паспорт']} ${formatDate(rowData['Коли видано паспорт'])}`,

    // Дані для машини і причепа
    '{{№ Автомобіля}}': rowData['№ Автомобіля'],
    '{{№ причепа}}': rowData['№ причепа'],
    '{{Марка автомобіля}}': rowData['Марка автомобіля'],
    '{{Марка причепа}}': rowData['Марка причепа'],
    '{{Модель автомобіля}}': rowData['Модель автомобіля'],
    '{{Модель причепа}}': rowData['Модель причепа'],
    '{{Тип автомобіля}}': rowData['Тип автомобіля'],
    '{{Тип причепа}}': rowData['Тип причепа'],
    '{{Довжина автомобіля}}': convertToMillimeters(rowData['Довжина автомобіля м.']),
    '{{Довжина причепа}}': convertToMillimeters(rowData['Довжина причепа м.']),
    '{{Ширина автомобіля}}': convertToMillimeters(rowData['Ширина автомобіля м.']),
    '{{Ширина причепа}}': convertToMillimeters(rowData['Ширина причепа м.']),
    '{{Висота автомобіля}}': convertToMillimeters(rowData['Висота автомобіля м.']),
    '{{Висота причепа}}': convertToMillimeters(rowData['Висота причепа м.']),
    '{{Вага автомобіля кг.}}': rowData['Вага автомобіля кг.'],
    '{{Вага причепа кг.}}': rowData['Вага причепа кг.'],
    '{{Вага автомобіля з вантажем кг.}}': rowData['Вага автомобіля з вантажем кг.'],
    '{{Вага причепа з вантажем кг.}}': rowData['Вага причепа з вантажем кг.']
  };

  for (const [placeholder, value] of Object.entries(placeholders)) {
    body.replaceText(placeholder, value || '');
  }

  doc.saveAndClose();
  ui.alert(`Заявку №${sequenceNumber} заповнено та збережено: ${document.getName()}`);
}

function formatDate(dateString) {
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}.${month}.${year}`;
}

function convertToMillimeters(meters) {
  if (!meters || isNaN(meters)) {
    return '';
  }
  return (parseFloat(meters) * 1000).toFixed(0);
}
