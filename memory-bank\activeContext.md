# Active Context

**Поточний фокус роботи:** Виправлення помилки перезапису значення резервуару, навіть якщо автоматичний вибір не був обраний, та врахування паралельних насосів у логіці автоматичного вибору резервуарів та часу зливу для дизельного палива у файлі `Заповненя акту з телеграм ботом.js`.

**Останні зміни:**
- Прочитано всі файли Memory Bank для відновлення контексту.
- Проаналізовано код у файлі `Заповненя акту з телеграм ботом.js`, зокрема логіку автоматичного визначення часу розвантаження та вибору резервуару.
- Визначено, що попередня логіка могла обирати зайняті резервуари, незважаючи на наявність вільних в інших групах.
- Внесено зміни до функції `createResponseDocument` для реалізації пріоритетного вибору резервуарів з вільних груп для забезпечення паралельного зливу.
- Реалізовано логіку вибору групи з найбільшою кількістю вільних резервуарів серед вільних груп.
- Реалізовано логіку вибору групи, яка звільниться найраніше, якщо всі групи зайняті.
- Час початку зливу встановлюється на поточний час для вільних груп або на час звільнення для зайнятих груп.
- Вибраний резервуар (з найбільшим номером) та розрахований час початку зливу записуються в таблицю "Розмитнення".
- Користувач підтвердив, що вибір конкретного номера резервуару в межах вибраної групи не є суттєвим, і поточна логіка вибору резервуару з найбільшим номером є прийнятною.
- Проведено симуляцію роботи оновленої логіки для сценарію з 5 дизельними та 1 бензиновою машиною, що прибувають одночасно, та продемонстровано, як машини будуть поставлені на злив з урахуванням доступних резервуарів та пріоритету вільних груп.
- Виявлено та виправлено помилку: відсутність виклику функції `removeTank` після автоматичного вибору резервуару призводила до повторного використання резервуарів. Додано виклик `removeTank` для видалення вибраного резервуару зі списку доступних.
- Виявлено, що логіка не враховує наявність двох паралельних насосів для дизеля.
- Оновлено логіку визначення часу початку зливу для дизеля, щоб врахувати два насоси, аналізуючи час звільнення другого найранішого активного дизельного розвантаження.
- Спрощено логіку вибору конкретного дизельного резервуару, обираючи найбільший номер серед усіх доступних дизельних резервуарів.
- Використано `write_to_file` для оновлення файлу `Заповненя акту з телеграм ботом.js` після невдалих спроб `replace_in_file`.
- **Виправлено проблему перезапису резервуару:**
    - Ініціалізація `selectedTank` перенесена на початок функції `createResponseDocument` для збереження ручного введення.
    - Логіка автоматичного вибору резервуару всередині блоку автоматичного часу тепер обгорнута додатковою умовою `if (responseData['Вибрати резервуар автоматично'] && responseData['Вибрати резервуар автоматично'].toLowerCase() === 'так')`.
    - Виклик `removeTank` перенесено всередину блоку автоматичного вибору резервуару.
- **Виправлено помилку повторного оголошення змінної `selectedTank`:** Змінено `let selectedTank` на `selectedTank =` у функції `createResponseDocument` для уникнення повторного оголошення.

**Наступні кроки:**
- Користувачу необхідно протестувати оновлену логіку, заповнивши та надіславши форму.
- Оновити файли Memory Bank (`progress.md`) на основі отриманої інформації.

**Важливі рішення та міркування:**
- Змінено логіку вибору резервуарів для дизеля, щоб надати пріоритет вільним групам для паралельного зливу.
- Забезпечено вибір резервуару з групи, яка звільниться найраніше, якщо всі групи зайняті.
- Час початку зливу тепер коректно встановлюється залежно від статусу вибраної групи (вільна/зайнята).
- Підтверджено, що специфічний вибір номера резервуару в межах групи (поточна логіка обирає найбільший номер) не є критичним для функціональності.
- Симуляція підтвердила, що оновлена логіка відповідає вимогам щодо паралельного зливу, коли є вільні групи резервуарів.
- Виправлено критичну помилку, пов'язану з повторним використанням резервуарів, шляхом додавання виклику `removeTank`.
- Враховано наявність двох дизельних насосів для більш точного розрахунку часу початку зливу.
- Спрощення логіки вибору резервуару для дизеля робить код зрозумілішим.
- Використання `write_to_file` як запасного варіанту дозволило успішно внести зміни після проблем з `replace_in_file`.
- **Виправлення перезапису резервуару:** Забезпечено, що ручне введення резервуару зберігається, якщо автоматичний вибір не активований, а автоматичний вибір та видалення резервуару зі списку вільних відбувається лише за умови явного вибору автоматичного режиму.
- **Виправлення помилки повторного оголошення:** Забезпечено коректне використання змінної `selectedTank` без конфліктів області видимості.

**Навчання та інсайти проекту:**
- Важливо чітко визначати пріоритети при виборі ресурсів (резервуарів) на основі їх доступності та можливості паралельного використання.
- Детальний аналіз існуючої логіки допоміг виявити причину некоректної поведінки.
- Уточнення вимог користувача щодо несуттєвості конкретного номера резервуару в межах групи дозволило підтвердити коректність поточної реалізації цього аспекту.
- Симуляція конкретних сценаріїв є корисним інструментом для перевірки та демонстрації поведінки складної логіки.
- Необхідно ретельно перевіряти всі побічні ефекти (наприклад, оновлення списку доступних ресурсів) після внесення змін до логіки вибору.
- Важливо враховувати всі паралельні ресурси (насоси) при розрахунку часу доступності.
- Іноді `write_to_file` є більш надійним способом внесення змін, якщо `replace_in_file` стикається з проблемами синхронізації або форматування.
- **Урок з перезапису:** Важливо чітко розмежовувати логіку автоматичного заповнення та ручного введення, щоб уникнути небажаних перезаписів даних.
- **Урок з оголошення змінних:** Важливо дотримуватися правил оголошення змінних у JavaScript, щоб уникнути помилок області видимості.

**Бізнес-логіка:**
- **Конфігурація резервуарів:**
    - Дизельні резервуари: Групи 1-8, 9-16, 29-33. Кожна група може мати свої особливості або пріоритети.
    - Бензинові резервуари: Група 17-24.
    - Логіка вибору резервуарів пріоритезує вільні групи для паралельного зливу. Якщо всі групи зайняті, обирається група, яка звільниться найраніше.
- **Обмеження насосів та призначення груп:**
    - Для дизельного палива є два паралельні насоси, що дозволяє одночасно розвантажувати дві машини. Логіка розрахунку часу зливу враховує це.
    - Кожен насос може бути призначений певній групі резервуарів або типу палива.
- **Час зливу:** Розраховується на основі типу палива, доступності резервуарів та насосів. Для дизеля враховується можливість паралельного зливу.

**Процеси генерації документів:**
- **Шаблони документів:** Використовуються Google Docs як шаблони для генерації документів.
- **Заповнення даних:** Дані з Google Sheets та Google Forms використовуються для заповнення відповідних полів у шаблонах.
- **Дата/час:** Коректна обробка дати та часу для документів, включаючи часові пояси та формати.
- **Специфікації складу:** Включення інформації про склад, куди доставляється паливо.
- **Розрахунок щільності:** Автоматичний розрахунок щільності палива для коректного заповнення документів.
- **Типи документів:**
    - **ВМД (Вантажна Митна Декларація):** Основний документ для розмитнення.
    - **Акти:** Документи, що підтверджують певні дії (наприклад, злив палива).
    - **Договори:** Юридичні документи.
    - **Заяви:** Різні типи заяв (наприклад, на перевезення).
    - **RMPD100:** Специфічний документ, пов'язаний з паливом.
- **Зберігання та надсилання:** Згенеровані документи зберігаються на Google Drive та можуть бути надіслані через Telegram або електронну пошту.

**Відомі проблеми та обмеження:**
- **Помилка повторного використання резервуарів:** Виправлено шляхом додавання `removeTank`.
- **Не враховано два паралельні насоси для дизеля:** Виправлено в логіці розрахунку часу зливу.
- **Проблеми з дозволами Google Apps Script:** Можуть виникати помилки `Exception: Unexpected error while getting the method or property openById on object SpreadsheetApp.` через некоректні дозволи або ID таблиці/аркуша.
- **Зберігання ключів API:** Ключі API наразі зберігаються безпосередньо в коді, що є потенційною проблемою безпеки.
- **Залежність від Google Services:** Проект повністю залежить від інфраструктури Google Apps Script, що може обмежувати масштабованість або переносимість.
- **Відсутність комплексного тестування:** Хоча проводяться симуляції, відсутність автоматизованих тестів може призвести до виявлення помилок лише на етапі експлуатації.
- **Обробка помилок:** Необхідно покращити механізми обробки помилок та логування для кращої діагностики проблем.
