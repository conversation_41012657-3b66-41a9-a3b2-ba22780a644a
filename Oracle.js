// --- Конфігурація ---
const oracleReq_TEMPLATE_ID = '1zYGnsLLeem8KWhxb5SpmtPL2jqF1XYL5EB1p94nJBp8'; // ID шаблону документа
const oracleReq_TARGET_FOLDER_ID = '1w6PxVqOlGz1uhPPk11LvrzVchDKBXvxE'; // ID цільової папки для збереження документів
const oracleReq_SHEET_NAME = 'Реєстр водіїв і автомобілів'; // Назва аркуша в таблиці
const oracleReq_BOT_TOKEN = '7461294369:AAHAsQ-2bVU8hxpeW7heWeAX7UTLa20yaHk'; // Токен Telegram бота
const oracleReq_CHAT_ID = '-4631002061'; // ID чату для надсилання сповіщень в групу

// Назви стовпців у таблиці
const oracleReq_COLUMN_DRIVER_NAME_EN = 'І.П. Англійською';
const oracleReq_COLUMN_DRIVER_ADR_NUM = 'ADR номер водія';
const oracleReq_COLUMN_DRIVER_ADR_DATE = 'ADR водія дійсний до';
const oracleReq_COLUMN_VEHICLE_NUM = '№ Автомобіля';
const oracleReq_COLUMN_VEHICLE_ADR_NUM = 'ADR номер машини';
const oracleReq_COLUMN_VEHICLE_ADR_DATE = 'ADR машини дійсний до';
const oracleReq_COLUMN_VEHICLE_BRAND = 'Марка автомобіля';
const oracleReq_COLUMN_VEHICLE_WEIGHT = 'Вага автомобіля кг.';
const oracleReq_COLUMN_VEHICLE_LOADED_WEIGHT = 'Вага автомобіля з вантажем кг.';
const oracleReq_COLUMN_TRAILER_NUM = '№ причепа';
const oracleReq_COLUMN_TRAILER_ADR_NUM = 'ADR номер причепа';
const oracleReq_COLUMN_TRAILER_ADR_DATE = 'ADR причепа дійсний до';
const oracleReq_COLUMN_TRAILER_BRAND = 'Марка причепа';
const oracleReq_COLUMN_TRAILER_WEIGHT = 'Вага причепа кг.';
const oracleReq_COLUMN_TRAILER_LOADED_WEIGHT = 'Вага причепа з вантажем кг.';

// Плейсхолдери в шаблоні документа
const oracleReq_PLACEHOLDER_DRIVER_NAME_EN = '{{І.П. Англійською}}';
const oracleReq_PLACEHOLDER_DRIVER_ADR_NUM = '{{ADR номер водія}}';
const oracleReq_PLACEHOLDER_DRIVER_ADR_DATE = '{{ADR водія дійсний до}}';
const oracleReq_PLACEHOLDER_VEHICLE_NUM = '{{№ Автомобіля}}';
const oracleReq_PLACEHOLDER_VEHICLE_ADR_NUM = '{{ADR номер машини}}';
const oracleReq_PLACEHOLDER_VEHICLE_ADR_DATE = '{{ADR машини дійсний до}}';
const oracleReq_PLACEHOLDER_VEHICLE_BRAND = '{{Марка автомобіля}}';
const oracleReq_PLACEHOLDER_VEHICLE_WEIGHT = '{{Вага автомобіля кг.}}';
const oracleReq_PLACEHOLDER_VEHICLE_LOADED_WEIGHT = '{{Вага автомобіля з вантажем кг.}}';
const oracleReq_PLACEHOLDER_TRAILER_NUM = '{{№ причепа}}';
const oracleReq_PLACEHOLDER_TRAILER_ADR_NUM = '{{ADR номер причепа}}';
const oracleReq_PLACEHOLDER_TRAILER_ADR_DATE = '{{ADR причепа дійсний до}}';
const oracleReq_PLACEHOLDER_TRAILER_BRAND = '{{Марка причепа}}';
const oracleReq_PLACEHOLDER_TRAILER_WEIGHT = '{{Вага причепа кг.}}';
const oracleReq_PLACEHOLDER_TRAILER_LOADED_WEIGHT = '{{Вага причепа з вантажем кг.}}';

// --- Головна функція ---
/**
 * Генерує документи Oracle Request для всіх автомобілів у реєстрі, перетворює їх у PDF та надсилає в Telegram.
 */
function oracleReq_generateAllRequests() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getSheetByName(oracleReq_SHEET_NAME);
  if (!sheet) {
    Logger.log(`Аркуш з назвою "${oracleReq_SHEET_NAME}" не знайдено.`);
    SpreadsheetApp.getUi().alert(`Аркуш з назвою "${oracleReq_SHEET_NAME}" не знайдено.`);
    return;
  }

  const dataRange = sheet.getDataRange();
  const values = dataRange.getValues();
  
  // Отримуємо заголовки і шукаємо індекси стовпців
  const headers = values[0];
  const columnIndices = findColumnIndices(headers);
  
  // Перевіряємо, чи знайдено всі необхідні стовпці
  if (!columnIndices[oracleReq_COLUMN_VEHICLE_NUM]) {
    Logger.log(`Стовпець "${oracleReq_COLUMN_VEHICLE_NUM}" не знайдено в таблиці.`);
    SpreadsheetApp.getUi().alert(`Стовпець "${oracleReq_COLUMN_VEHICLE_NUM}" не знайдено в таблиці.`);
    return;
  }

  // Перевіряємо, чи вказано токен бота
  if (!oracleReq_BOT_TOKEN) {
    Logger.log('Токен Telegram бота не вказано. Встановіть значення для константи oracleReq_BOT_TOKEN.');
    SpreadsheetApp.getUi().alert('Токен Telegram бота не вказано. Встановіть значення для константи oracleReq_BOT_TOKEN.');
    return;
  }

  const targetFolder = DriveApp.getFolderById(oracleReq_TARGET_FOLDER_ID);
  const templateFile = DriveApp.getFileById(oracleReq_TEMPLATE_ID);

  // Пропускаємо рядок заголовка (починаємо з i = 1)
  for (let i = 1; i < values.length; i++) {
    const row = values[i];

    // Отримуємо значення за назвою стовпця
    const vehicleNum = getValueByColumnName(row, columnIndices, oracleReq_COLUMN_VEHICLE_NUM);

    // Перевірка на порожній рядок або відсутність номера авто
    if (row.join("").trim() === "" || !vehicleNum) {
      Logger.log(`Пропуск рядка ${i + 1}: порожній або без номера автомобіля.`);
      continue;
    }

    // Отримуємо всі дані з рядка за назвами стовпців
    const driverNameEN = getValueByColumnName(row, columnIndices, oracleReq_COLUMN_DRIVER_NAME_EN);
    const driverAdrNum = getValueByColumnName(row, columnIndices, oracleReq_COLUMN_DRIVER_ADR_NUM);
    const driverAdrDate = getValueByColumnName(row, columnIndices, oracleReq_COLUMN_DRIVER_ADR_DATE);
    const vehicleAdrNum = getValueByColumnName(row, columnIndices, oracleReq_COLUMN_VEHICLE_ADR_NUM);
    const vehicleAdrDate = getValueByColumnName(row, columnIndices, oracleReq_COLUMN_VEHICLE_ADR_DATE);
    const vehicleBrand = getValueByColumnName(row, columnIndices, oracleReq_COLUMN_VEHICLE_BRAND);
    const vehicleWeight = getValueByColumnName(row, columnIndices, oracleReq_COLUMN_VEHICLE_WEIGHT);
    const vehicleLoadedWeight = getValueByColumnName(row, columnIndices, oracleReq_COLUMN_VEHICLE_LOADED_WEIGHT);
    const trailerNum = getValueByColumnName(row, columnIndices, oracleReq_COLUMN_TRAILER_NUM);
    const trailerAdrNum = getValueByColumnName(row, columnIndices, oracleReq_COLUMN_TRAILER_ADR_NUM);
    const trailerAdrDate = getValueByColumnName(row, columnIndices, oracleReq_COLUMN_TRAILER_ADR_DATE);
    const trailerBrand = getValueByColumnName(row, columnIndices, oracleReq_COLUMN_TRAILER_BRAND);
    const trailerWeight = getValueByColumnName(row, columnIndices, oracleReq_COLUMN_TRAILER_WEIGHT);
    const trailerLoadedWeight = getValueByColumnName(row, columnIndices, oracleReq_COLUMN_TRAILER_LOADED_WEIGHT);

    // Форматуємо дати ADR, якщо вони є
    const formattedDriverAdrDate = formatDate(driverAdrDate, i);
    const formattedVehicleAdrDate = formatDate(vehicleAdrDate, i);
    const formattedTrailerAdrDate = formatDate(trailerAdrDate, i);

    Logger.log(`Обробка авто: ${vehicleNum}, Чат ID: ${oracleReq_CHAT_ID}`);

    try {
      // 1. Створити копію документа
      const newFileName = `Oracle Request - ${vehicleNum} - ${Utilities.formatDate(new Date(), Session.getScriptTimeZone(), 'yyyy-MM-dd')}`;
      const newFile = templateFile.makeCopy(newFileName, targetFolder);
      const newDoc = DocumentApp.openById(newFile.getId());
      const body = newDoc.getBody();

      // 2. Заповнити плейсхолдери
      body.replaceText(oracleReq_PLACEHOLDER_DRIVER_NAME_EN, driverNameEN || '');
      body.replaceText(oracleReq_PLACEHOLDER_DRIVER_ADR_NUM, driverAdrNum || '');
      body.replaceText(oracleReq_PLACEHOLDER_DRIVER_ADR_DATE, formattedDriverAdrDate || '');
      body.replaceText(oracleReq_PLACEHOLDER_VEHICLE_NUM, vehicleNum || '');
      body.replaceText(oracleReq_PLACEHOLDER_VEHICLE_ADR_NUM, vehicleAdrNum || '');
      body.replaceText(oracleReq_PLACEHOLDER_VEHICLE_ADR_DATE, formattedVehicleAdrDate || '');
      body.replaceText(oracleReq_PLACEHOLDER_VEHICLE_BRAND, vehicleBrand || '');
      body.replaceText(oracleReq_PLACEHOLDER_VEHICLE_WEIGHT, vehicleWeight || '');
      body.replaceText(oracleReq_PLACEHOLDER_VEHICLE_LOADED_WEIGHT, vehicleLoadedWeight || '');
      body.replaceText(oracleReq_PLACEHOLDER_TRAILER_NUM, trailerNum || '');
      body.replaceText(oracleReq_PLACEHOLDER_TRAILER_ADR_NUM, trailerAdrNum || '');
      body.replaceText(oracleReq_PLACEHOLDER_TRAILER_ADR_DATE, formattedTrailerAdrDate || '');
      body.replaceText(oracleReq_PLACEHOLDER_TRAILER_BRAND, trailerBrand || '');
      body.replaceText(oracleReq_PLACEHOLDER_TRAILER_WEIGHT, trailerWeight || '');
      body.replaceText(oracleReq_PLACEHOLDER_TRAILER_LOADED_WEIGHT, trailerLoadedWeight || '');

      // 3. Зберегти та закрити документ
      newDoc.saveAndClose();

      // 4. Створити PDF-файл
      const pdfBlob = newFile.getAs('application/pdf');
      pdfBlob.setName(newFileName + '.pdf');

      // 5. Надіслати PDF у Telegram
      if (oracleReq_CHAT_ID) {
        const caption = `📄 Створено запит Oracle для автомобіля *${vehicleNum}*.\n\n` +
                        `Водій: ${driverNameEN || ''}\n` +
                        `Марка автомобіля: ${vehicleBrand || ''}\n` +
                        `ADR автомобіля №: ${vehicleAdrNum || ''}\n` +
                        `ADR автомобіля дійсний до: ${formattedVehicleAdrDate || ''}\n` +
                        `Причіп №: ${trailerNum || ''}\n` +
                        `Марка причепа: ${trailerBrand || ''}`;
        oracleReq_sendTelegramDocument(oracleReq_BOT_TOKEN, oracleReq_CHAT_ID, pdfBlob, caption);
      } else {
        Logger.log(`Chat ID не вказано. Сповіщення не надіслано для авто ${vehicleNum}.`);
      }

    } catch (e) {
      Logger.log(`Помилка обробки рядка ${i + 1} (Авто: ${vehicleNum}): ${e}`);
    }
    // Невелика пауза для уникнення лімітів Google API
    Utilities.sleep(500);
  }

  Logger.log('Генерація всіх документів завершена.');
  SpreadsheetApp.getUi().alert('Генерація всіх документів завершена.');
}

/**
 * Знаходить індекси стовпців за їхніми назвами.
 * @param {Array} headers Масив заголовків стовпців.
 * @return {Object} Об'єкт, де ключ - назва стовпця, значення - індекс.
 */
function findColumnIndices(headers) {
  const indices = {};
  for (let i = 0; i < headers.length; i++) {
    const header = headers[i];
    if (header) {
      indices[header] = i;
    }
  }
  return indices;
}

/**
 * Отримує значення з рядка за назвою стовпця.
 * @param {Array} row Масив значень рядка.
 * @param {Object} columnIndices Об'єкт з індексами стовпців.
 * @param {string} columnName Назва стовпця.
 * @return {*} Значення або порожній рядок, якщо стовпець не знайдено.
 */
function getValueByColumnName(row, columnIndices, columnName) {
  const index = columnIndices[columnName];
  if (index !== undefined) {
    return row[index] || '';
  }
  return '';
}

/**
 * Форматує дату в рядковий формат dd.MM.yyyy.
 * @param {Object} dateValue Значення дати з таблиці.
 * @param {number} rowIndex Індекс рядка для логування.
 * @return {string} Відформатована дата або порожній рядок, якщо дата недійсна.
 */
function formatDate(dateValue, rowIndex) {
  if (!dateValue) return '';
  if (dateValue instanceof Date) {
    return Utilities.formatDate(dateValue, Session.getScriptTimeZone(), 'dd.MM.yyyy');
  }
  try {
    let parsedDate = new Date(dateValue);
    if (!isNaN(parsedDate.getTime())) {
      return Utilities.formatDate(parsedDate, Session.getScriptTimeZone(), 'dd.MM.yyyy');
    } else {
      return dateValue.toString();
    }
  } catch (e) {
    Logger.log(`Не вдалося розпізнати дату "${dateValue}" в рядку ${rowIndex + 1}. Використовується як текст.`);
    return dateValue.toString();
  }
}

/**
 * Надсилає документ у Telegram.
 * @param {string} oracleReq_BOT_TOKEN Токен Telegram бота.
 * @param {string|number} oracleReq_CHAT_ID ID чату або користувача.
 * @param {Blob} documentBlob Blob-об'єкт документа для надсилання.
 * @param {string} caption Підпис до документа (підтримує Markdown).
 */
function oracleReq_sendTelegramDocument(oracleReq_BOT_TOKEN, oracleReq_CHAT_ID, documentBlob, caption) {
  const apiUrl = `https://api.telegram.org/bot${oracleReq_BOT_TOKEN}/sendDocument`;
  const formData = {
    chat_id: String(oracleReq_CHAT_ID),
    caption: caption,
    parse_mode: 'Markdown',
    document: documentBlob
  };

  const options = {
    method: 'post',
    payload: formData,
    muteHttpExceptions: true
  };

  try {
    const response = UrlFetchApp.fetch(apiUrl, options);
    const responseCode = response.getResponseCode();
    const responseBody = response.getContentText();
    if (responseCode === 200) {
      Logger.log(`Документ успішно надіслано в чат ${oracleReq_CHAT_ID}.`);
    } else {
      Logger.log(`Помилка надсилання документа в чат ${oracleReq_CHAT_ID}. Код: ${responseCode}, Відповідь: ${responseBody}`);
    }
  } catch (error) {
    Logger.log(`Помилка виконання запиту до Telegram API: ${error}`);
  }
}