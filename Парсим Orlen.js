// --- Унікальні функції та константи для Orlen Monitor ---
const orlenMonConfig = {
    LOGIN_URL: 'https://e-procurement.orlen.pl/OA_HTML/AppsLocalLogin.jsp',
    DOCUMENTS_URL: 'https://e-procurement.orlen.pl/OA_HTML/OA.jsp',
    USERNAME: 'OLEGDZUMAGA',
    PASSWORD: 'orlen124',
    FOLDER_ID: '19pcDbPGnJP4IDKjDQGYksW21du38Ha07'
  };
  
  function orlenMonGetOrigin(url) {
    if (!url) return null;
    const match = url.match(/^(https?:\/\/[^\/]+)/i);
    return match ? match[1] : null;
  }
  function orlenMonGetBaseUrl(fullUrl) {
    return orlenMonGetOrigin(fullUrl);
  }
  function orlenMonMakeAbsoluteUrl(baseUrl, relativePath) {
    if (!baseUrl || !relativePath || typeof relativePath !== 'string' || relativePath.startsWith('http') || relativePath.startsWith('javascript:')) {
      return relativePath;
    }
    const origin = orlenMonGetOrigin(baseUrl);
    if (!origin) return relativePath;
    if (relativePath.startsWith('/')) {
      return origin + relativePath;
    } else {
      const pathStartIndex = baseUrl.indexOf('//') > -1 ? baseUrl.indexOf('/', baseUrl.indexOf('//') + 2) : baseUrl.indexOf('/');
      const basePath = (pathStartIndex > -1 && baseUrl.lastIndexOf('/') > pathStartIndex) ? baseUrl.substring(0, baseUrl.lastIndexOf('/') + 1) : origin + '/';
      return basePath + relativePath;
    }
  }
  
  function orlenMonMain() {
    try {
      const loginResult = orlenMonLogin();
      if (!loginResult || !loginResult.cookieHeader || !loginResult.finalUrl) return;
      const cookieHeader = loginResult.cookieHeader;
      const startUrl = loginResult.finalUrl;
      const getDocsResult = orlenMonGetDocuments(cookieHeader, startUrl);
      if (!getDocsResult || !getDocsResult.docPreviewHtml) return;
      const allDocs = orlenMonParseDocumentsTable(getDocsResult.docPreviewHtml);
      if (allDocs.length === 0) return;
      const lastDocNumber = orlenMonGetLastDocNumber();
      const newestDoc = allDocs[0];
      if (newestDoc && newestDoc.number && newestDoc.number !== lastDocNumber) {
        Logger.log('Знайдено нову інформацію про машину. Відправляємо сповіщення в Telegram...');
        orlenMonSendTelegramForNewTruck(allDocs);
        orlenMonSetLastDocNumber(newestDoc.number);
        Logger.log('Сповіщення в Telegram відправлено.');
      }
    } catch (error) {
      console.error('Orlen Monitor критична помилка:', error);
    }
  }
  
  function orlenMonLogin() {
    try {
      let currentCookies = {};
      const updateCookies = (response) => {
        const headers = response.getAllHeaders();
        const setCookieHeaders = headers['Set-Cookie'] || headers['set-cookie'];
        if (setCookieHeaders) {
          const cookiesArray = Array.isArray(setCookieHeaders) ? setCookieHeaders : [setCookieHeaders];
          cookiesArray.forEach(cookieString => {
            const parts = cookieString.split(';')[0].split('=');
            if (parts.length >= 2) {
              const key = parts[0].trim();
              const value = parts.slice(1).join('=').trim();
              if (value || !currentCookies[key]) currentCookies[key] = value;
            }
          });
        }
      };
      const formatCookieHeader = () => Object.entries(currentCookies).map(([key, value]) => `${key}=${value}`).join('; ');
      const initialResponse = UrlFetchApp.fetch(orlenMonConfig.LOGIN_URL, {
        method: 'get',
        followRedirects: true,
        muteHttpExceptions: true,
        validateHttpsCertificates: false,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36'
        }
      });
      updateCookies(initialResponse);
      const initialHtml = initialResponse.getContentText();
      const formActionRegex = /<form[^>]*action=[\'\"]([^\'\"]+)[\'\"][^>]*>/i;
      const actionMatch = initialHtml.match(formActionRegex);
      let postUrl = null;
      const loginOrigin = orlenMonGetOrigin(orlenMonConfig.LOGIN_URL);
      if (actionMatch && actionMatch[1]) {
        const actionPath = actionMatch[1];
        if (actionPath.startsWith('http')) {
          postUrl = actionPath;
        } else if (loginOrigin) {
          postUrl = loginOrigin + '/OA_HTML/' + (actionPath.startsWith('/') ? actionPath.substring(1) : actionPath);
        } else {
          postUrl = orlenMonConfig.LOGIN_URL;
        }
      } else {
        postUrl = orlenMonConfig.LOGIN_URL;
      }
      if (!postUrl) return null;
      const hiddenFields = {};
      const hiddenInputRegex = /<input[^>]*?type\s*=\s*[\'\"]hidden[\'\"][^>]*?>/gi;
      const nameRegex = /name\s*=\s*[\'\"]([^\"]+)[\'\"]/i;
      const valueRegex = /value\s*=\s*[\'\"]([^\"]*)[\'\"]/i;
      let match;
      while ((match = hiddenInputRegex.exec(initialHtml)) !== null) {
        const inputTag = match[0];
        const nameMatch = inputTag.match(nameRegex);
        const valueMatch = inputTag.match(valueRegex);
        if (nameMatch && nameMatch[1]) {
          const name = nameMatch[1];
          const value = (valueMatch && valueMatch[1]) ? valueMatch[1].replace(/&quot;/g, '"').replace(/&amp;/g, '&') : '';
          hiddenFields[name] = value;
        }
      }
      const formData = {
        'username': orlenMonConfig.USERNAME,
        'password': orlenMonConfig.PASSWORD,
        'locale': 'US',
        'cancelUrl': 'OA.jsp',
        'loginButtonName': 'Login',
        'login': 'true',
        'OAM_REQ': 'true'
      };
      Object.assign(formData, hiddenFields);
      let cookieForLogin = formatCookieHeader();
      const loginOptions = {
        method: 'post',
        payload: formData,
        followRedirects: false, // Це важливо, щоб не відправляти нас на сайт з капчею на Новий Рік
        muteHttpExceptions: true,
        validateHttpsCertificates: false,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Origin': loginOrigin || '',
          'Referer': orlenMonConfig.LOGIN_URL,
          'Cookie': cookieForLogin
        }
      };
      const loginResponse = UrlFetchApp.fetch(postUrl, loginOptions);
      updateCookies(loginResponse);
      let finalResponse = loginResponse;
      let currentLocation = postUrl;
      for (let i = 0; i < 5 && finalResponse.getResponseCode() === 302; i++) {
        const locationHeader = finalResponse.getAllHeaders()['Location'] || finalResponse.getAllHeaders()['location'];
        if (locationHeader) {
          let nextLocation = locationHeader;
          if (!nextLocation.startsWith('http')) {
            const currentOrigin = orlenMonGetOrigin(currentLocation);
            if (currentOrigin) {
              if (nextLocation.startsWith('/')) {
                nextLocation = currentOrigin + nextLocation;
              } else {
                const lastSlashIndex = currentLocation.indexOf('//') > -1 ? currentLocation.indexOf('/', currentLocation.indexOf('//') + 2) : currentLocation.indexOf('/');
                const basePath = lastSlashIndex > -1 ? currentLocation.substring(0, currentLocation.lastIndexOf('/') + 1) : currentLocation + '/';
                nextLocation = basePath + nextLocation;
              }
            } else {
              nextLocation = loginOrigin ? (loginOrigin + (nextLocation.startsWith('/') ? nextLocation : '/' + nextLocation)) : nextLocation;
            }
          }
          const previousLocation = currentLocation;
          currentLocation = nextLocation;
          let currentCookieHeader = formatCookieHeader();
          finalResponse = UrlFetchApp.fetch(currentLocation, {
            method: 'get',
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
              'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
              'Referer': previousLocation,
              'Cookie': currentCookieHeader
            },
            followRedirects: false,
            muteHttpExceptions: true,
            validateHttpsCertificates: false
          });
          updateCookies(finalResponse);
        } else {
          return null;
        }
      }
      Utilities.sleep(2000);
      let finalCookieHeader = formatCookieHeader();
      let finalUrlAfterRedirects = currentLocation;
      if (!finalCookieHeader || Object.keys(currentCookies).length === 0) return null;
      const testOptions = {
        method: 'get',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Referer': finalUrlAfterRedirects,
          'Cookie': finalCookieHeader
        },
        followRedirects: true,
        muteHttpExceptions: true,
        validateHttpsCertificates: false
      };
      const testResponse = UrlFetchApp.fetch(orlenMonConfig.DOCUMENTS_URL + '?OAFunc=OAHOMEPAGE', testOptions);
      updateCookies(testResponse);
      finalCookieHeader = formatCookieHeader();
      let checkHtml;
      try {
        checkHtml = finalResponse.getContentText();
      } catch (e) {
        checkHtml = testResponse.getContentText();
      }
      if (!checkHtml) checkHtml = testResponse.getContentText();
      const isLoggedIn = checkHtml.includes('OALogout.jsp') || checkHtml.includes('>Logout<') || checkHtml.includes(orlenMonConfig.USERNAME);
      if (isLoggedIn) {
        return { cookieHeader: finalCookieHeader, finalUrl: finalUrlAfterRedirects };
      } else {
        return null;
      }
    } catch (error) {
      return null;
    }
  }
  
  function orlenMonGetDocuments(cookieHeader, startUrl) {
    try {
      const startPageResponse = UrlFetchApp.fetch(startUrl, {
        method: 'get',
        headers: {
          'Cookie': cookieHeader,
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
        },
        muteHttpExceptions: true,
        validateHttpsCertificates: false
      });
      if (startPageResponse.getResponseCode() !== 200) return null;
      const startPageHtml = startPageResponse.getContentText();
      const reportsLinkRegex = /<a\s+[^>]*href=([\'\"])([^\'\"#]+)\1[^>]*>\s*Reports\s*<\/a>/gi;
      let reportsMatch = null;
      let potentialMatches = startPageHtml.matchAll(reportsLinkRegex);
      for (const match of potentialMatches) {
        if (match[2] && !match[2].toLowerCase().includes('logout')) {
          reportsMatch = match;
          break;
        }
      }
      let reportsUrl = null;
      if (reportsMatch && reportsMatch[2]) {
        reportsUrl = orlenMonMakeAbsoluteUrl(startUrl, reportsMatch[2].replace(/&amp;/g, '&'));
        if (!reportsUrl) return null;
      } else {
        return null;
      }
      Utilities.sleep(1500);
      const reportsPageResponse = UrlFetchApp.fetch(reportsUrl, {
        method: 'get',
        headers: {
          'Cookie': cookieHeader,
          'Referer': startUrl,
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
        },
        muteHttpExceptions: true,
        validateHttpsCertificates: false
      });
      if (reportsPageResponse.getResponseCode() !== 200) return null;
      const reportsPageHtml = reportsPageResponse.getContentText();
      const docPreviewLinkRegex = /<a\s+[^>]*href=([\'\"])([^\'\"#]+)\1[^>]*>\s*Document Preview\s*<\/a>/gi;
      let docPreviewMatch = null;
      let potentialDocPreviewMatches = reportsPageHtml.matchAll(docPreviewLinkRegex);
      for (const match of potentialDocPreviewMatches) {
        if (match[2] && !match[2].toLowerCase().includes('logout')) {
          docPreviewMatch = match;
          break;
        }
      }
      let docPreviewUrl = null;
      if (docPreviewMatch && docPreviewMatch[2]) {
        docPreviewUrl = orlenMonMakeAbsoluteUrl(reportsUrl, docPreviewMatch[2].replace(/&amp;/g, '&'));
        if (!docPreviewUrl) return null;
      } else {
        return null;
      }
      Utilities.sleep(1500);
      const docPreviewPageResponse = UrlFetchApp.fetch(docPreviewUrl, {
        method: 'get',
        headers: {
          'Cookie': cookieHeader,
          'Referer': reportsUrl,
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
        },
        muteHttpExceptions: true,
        validateHttpsCertificates: false
      });
      if (docPreviewPageResponse.getResponseCode() !== 200) return null;
      const docPreviewHtml = docPreviewPageResponse.getContentText();
      return { docPreviewHtml: docPreviewHtml };
    } catch (error) {
      return null;
    }
  }
  
  function orlenMonParseDocumentsTable(html) {
    const tables = html.match(/<table[\s\S]*?<\/table>/gi) || [];
    if (tables.length < 13) return [];
    const tableHtml = tables[12];
    const rows = tableHtml.match(/<tr[\s\S]*?<\/tr>/gi) || [];
    let headers = [];
    const docs = [];
    rows.forEach((row, idx) => {
      const cells = row.match(/<t[dh][^>]*>([\s\S]*?)<\/t[dh]>/gi) || [];
      const cellTexts = cells.map(cell => cell.replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').replace(/\|--&gt;/g, '').trim());
      if (idx === 0) {
        headers = cellTexts;
        return;
      }
      if (cellTexts.length < 2) return;
      docs.push({
        type: cellTexts[0],
        number: cellTexts[1],
        product: cellTexts[2] || '',
        customer: cellTexts[3] || '',
        date: cellTexts[4] || '',
        amount: cellTexts[5] || '',
        currency: cellTexts[6] || '',
        language: cellTexts[7] || '',
        preview: cellTexts[8] || ''
      });
    });
    return docs;
  }
  
  function orlenMonGetLastDocNumber() {
    try {
      return PropertiesService.getScriptProperties().getProperty('orlenMonLastDocNumber') || '';
    } catch (e) { return ''; }
  }
  function orlenMonSetLastDocNumber(docNumber) {
    try {
      PropertiesService.getScriptProperties().setProperty('orlenMonLastDocNumber', docNumber);
    } catch (e) {}
  }
  function orlenMonClearLastDocNumber() {
    try {
      PropertiesService.getScriptProperties().deleteProperty('orlenMonLastDocNumber');
      Logger.log('Памʼять про останню машину очищено.');
    } catch (e) {
      Logger.log('Не вдалося очистити памʼять: ' + e);
    }
  }
  function orlenMonSendTelegramForNewTruck(docs) {
    if (!docs || docs.length < 3) return;
    const token = '7461294369:AAHAsQ-2bVU8hxpeW7heWeAX7UTLa20yaHk';
    const chatId = '-4631002061'; // chat_id групи
    // const chatId = '276789696';// чат зі мною
    const apiUrl = `https://api.telegram.org/bot${token}/sendMessage`;
    let text = 'Завантажилась машина,\nна сайті Orlen, з\'явились нові документи:';
    docs.slice(0, 3).forEach(doc => {
      text += `\n${doc.type}: ${doc.number}`;
    });
    const payload = {
      chat_id: chatId,
      text: text
    };
    try {
      UrlFetchApp.fetch(apiUrl, {
        method: 'post',
        contentType: 'application/json',
        payload: JSON.stringify(payload),
        muteHttpExceptions: true
      });
    } catch (e) {}
  }
