function fillVatStatus() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  
  // Отримуємо аркуші "Контрагенти" та "ПДВ"
  const contractorsSheet = ss.getSheetByName("Контрагенти");
  const vatSheet = ss.getSheetByName("ПДВ");
  
  // Зчитуємо дані з аркуша "Контрагенти" для скорочених назв контрагентів
  const contractorsData = contractorsSheet.getRange("A2:A" + contractorsSheet.getLastRow()).getValues();
  
  // Зчитуємо дані з аркуша "ПДВ" для скорочених назв контрагентів і статусу ПДВ
  const vatData = vatSheet.getRange("A2:E" + vatSheet.getLastRow()).getValues();
  
  // Створюємо об'єкт для зберігання статусу ПДВ кожного контрагента
  const vatStatusMap = {};

  // Проходимо по даних з аркуша "ПДВ" і заповнюємо об'єкт vatStatusMap
  vatData.forEach(row => {
    const contractorName = row[0]; // Скорочена назва контрагента (стовпець A)
    const vatStatus = row[4];      // Статус ПДВ (стовпець E)
    
    vatStatusMap[contractorName] = vatStatus;
  });

  // Заповнюємо аркуш "Контрагенти" статусом ПДВ
  contractorsData.forEach((contractor, i) => {
    const contractorName = contractor[0];
    const vatStatus = vatStatusMap[contractorName] || "";

    // Вставляємо значення статусу ПДВ у стовпець M
    contractorsSheet.getRange(i + 2, 13).setValue(vatStatus); // Стовпець M
  });
}
