# System Patterns

Система побудована на базі Google Apps Script і автоматизує документообіг, пов'язаний з імпортом палива. Ключові компоненти включають:

- **Модулі обробки документів:** Окремі файли для створення, заповнення та обробки різних типів документів.
    - `ОбробкаВМД.js`: Обробка вхідних ВМД.
    - `Друк ВМД і Актів.js`: Функції для друку ВМД та Актів.
    - `Заповнення заяви на перевезення.gs.js`: Заповнення заяв на перевезення.
    - `Заповнюємо договір.js`: Заповнення договорів.
    - `create_RMPD100.js`: Створення документів RMPD100.
    - `Формуваня не підписаних договорів.js`: Формування непідписаних договорів.

- **Інтеграція з зовнішніми сервісами:**
    - **Telegram Bot:** Використовується для надсилання документів та повідомлень.
        - `telegam_bot.js`: Основний модуль для взаємодії з Telegram Bot API.
        - `fuel_discharge_bot.js`: Специфічний бот для сповіщень про злив палива.
        - `Заповненя акту з телеграм ботом.js`: Логіка заповнення актів та взаємодії з ботом.
        - `SHIPPING APPLICATION з тг.js`: Основний файл для обробки запитів з Telegram та Google Forms, включаючи логіку вибору резервуарів та часу зливу.
        - `Читаєм RMPD.js`: Читання підтверджень RMPD, можливо, через Telegram.
    - **Парсер Orlen:** Модулі для моніторингу та парсингу даних з системи Orlen.
        - `Парсим Orlen.js`: Основна логіка парсингу.
        - `моніторинг_орлен.js`: Моніторинг даних Orlen.
    - **Поштові сервіси:** Читання та обробка вкладень з електронної пошти.
        - `Читаємо з пошти PDF CMR і Invoise.js`: Читання PDF CMR та Invoise з пошти.
    - **Система GPS/Wialon:** Функціонал для отримання даних GPS та ETA вантажівок.
        - `GPS.js`, `GPS2.js`: Модулі для взаємодії з GPS/Wialon.
    - **Система BAS/Oracle:** Модулі для створення XML файлів для імпорту в BAS та взаємодії з Oracle.
        - `BAS_Import.js`: Імпорт даних у BAS.
        - `Oracle.js`: Взаємодія з Oracle.

- **Функціонал для управління даними:**
    - `Заповнення контактної інформації.js`: Заповнення контактних даних.
    - `заповнення підписантів.js`: Заповнення інформації про підписантів.
    - `Заповнюємо ІПН ФОП.js`: Заповнення ІПН ФОП.
    - `заповнюємо статус ПДВ.js`: Заповнення статусу ПДВ.
    - `Переміщення контрагентів.js`: Переміщення даних контрагентів.

- **Утиліти:**
    - `Видалення печаток і підписів з документі.js`: Видалення печаток та підписів.
    - `sortVMDtoPDF.js`: Сортування ВМД для PDF.
    - `sync.js`: Скрипт для синхронізації коду (ймовірно, з Clasp).
    - `transferRmpdData.js`: Передача даних RMPD.

- **Управління процесами:**
    - `етапи обробки.js`: Функції для запуску та перевірки етапів обробки документів.
    - `перевірка гугл форм.js`: Перевірка даних, отриманих з Google Forms.

- **Інтерфейс користувача:**
    - `menu.gs.js`: Створення користувацького меню в Google Sheets.

- **Авторизація:** Використання бібліотеки OAuth2 для безпечного доступу до сервісів Google.

- **Використання розширених сервісів Google:** Drive, Docs для роботи з файлами та шаблонами документів.
