/**
 * Обробляє надсилання даних з форми та надсилає повідомлення в Telegram.
 * Ця функція призначена для використання як тригер Google Форми.
 * Примітка: Ця версія отримує доступ до таблиці за ID.
 */
function handleFuelFormSubmit() {
  try {
    const spreadsheet = SpreadsheetApp.openById(FUEL_DISCHARGE_BOT_SPREADSHEET_ID);
    const sheet = spreadsheet.getSheetById(FUEL_DISCHARGE_BOT_SHEET_ID);

    if (!sheet) {
      Logger.log(`Sheet with GID ${FUEL_DISCHARGE_BOT_SHEET_ID} not found in spreadsheet ${FUEL_DISCHARGE_BOT_SPREADSHEET_ID}.`);
      // TODO: Send error notification to Telegram?
      return;
    }

    // Read the header row to find column indices
    const headerRow = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
    const columnIndices = {};
    const requiredHeaders = [
      "Номер автомобіля і причепа",
      "Нафтопродукти злито в резервуар №",
      "Найменування вантажу",
      "Дата і час початку приймання вантажу",
      "Дата і час завершення розвантаження"
    ];

    requiredHeaders.forEach(header => {
      const index = headerRow.indexOf(header);
      if (index !== -1) {
        columnIndices[header] = index;
      } else {
        Logger.log(`Header "${header}" not found in the sheet.`);
        // TODO: Send error notification to Telegram?
        // Decide how to handle missing columns - maybe skip or report error
      }
    });

    // Check if all required headers were found (optional, depending on desired strictness)
    if (Object.keys(columnIndices).length !== requiredHeaders.length) {
       Logger.log("Not all required headers were found. Proceeding with available data.");
       // TODO: Send warning notification to Telegram?
    }


    // Read the last row of data
    const lastRowIndex = sheet.getLastRow();
    if (lastRowIndex < 2) { // Assuming row 1 is header
      Logger.log("No data rows found in the sheet.");
      return;
    }
    const lastRowData = sheet.getRange(lastRowIndex, 1, 1, sheet.getLastColumn()).getValues()[0];

    // Extract data using columnIndices
    const carNumber = columnIndices["Номер автомобіля і причепа"] !== undefined ? lastRowData[columnIndices["Номер автомобіля і причепа"]] : "N/A";
    const reservoirNumber = columnIndices["Нафтопродукти злито в резервуар №"] !== undefined ? lastRowData[columnIndices["Нафтопродукти злито в резервуар №"]] : "N/A";
    const cargoName = columnIndices["Найменування вантажу"] !== undefined ? lastRowData[columnIndices["Найменування вантажу"]] : "N/A";
    let startTime = columnIndices["Дата і час початку приймання вантажу"] !== undefined ? lastRowData[columnIndices["Дата і час початку приймання вантажу"]] : "N/A";
    let endTime = columnIndices["Дата і час завершення розвантаження"] !== undefined ? lastRowData[columnIndices["Дата і час завершення розвантаження"]] : "N/A";

    // Format dates if they are Date objects
    const timeZone = SpreadsheetApp.getActiveSpreadsheet().getSpreadsheetTimeZone();
    if (startTime instanceof Date) {
      startTime = Utilities.formatDate(startTime, timeZone, "dd.MM.yyyy HH:mm");
    }
     if (endTime instanceof Date) {
      endTime = Utilities.formatDate(endTime, timeZone, "dd.MM.yyyy HH:mm");
    }


    // Format the message
    const message = `
Новий злив палива:

Автомобіль: ${carNumber}
Резервуар №: ${reservoirNumber}
Вантаж: ${cargoName}
Початок зливу: ${startTime}
Кінець зливу: ${endTime}
`;

    // Send the message to Telegram
    if (typeof fuelDischargeBot_sendTelegramMessage === 'function') {
      // Передаємо правильні аргументи: спочатку Chat ID, потім повідомлення
      fuelDischargeBot_sendTelegramMessage(FUEL_DISCHARGE_BOT_CHAT_ID, message);
      // Лог успіху перенесено всередину функції fuelDischargeBot_sendTelegramMessage
    } else {
      Logger.log("Error: fuelDischargeBot_sendTelegramMessage function not found.");
      // TODO: Send error notification to Telegram?
    }


  } catch (e) {
    Logger.log(`Error in handleFuelFormSubmit: ${e.toString()}`);
    // TODO: Send error notification to Telegram?
  }
}

/**
 * Функція для відправки повідомлення в Telegram для цього бота.
 * Адаптовано з telegam_bot.js для використання унікального токена.
 * @param {string} chatId - ID чату/групи куди відправити повідомлення
 * @param {string} message - Текст повідомлення
 * @returns {boolean} Успішність відправки
 */
function fuelDischargeBot_sendTelegramMessage(chatId, message) {
  const MAX_RETRIES = 3; // Максимальна кількість спроб
  const RETRY_DELAY_MS = 2000; // Затримка між спробами в мілісекундах (2 секунди)
  let attempts = 0;

  while (attempts < MAX_RETRIES) {
    try {
      const url = `https://api.telegram.org/bot${FUEL_DISCHARGE_BOT_TELEGRAM_TOKEN}/sendMessage`;
      const payload = {
        chat_id: chatId,
        text: message,
        parse_mode: 'HTML'
      };

      const options = {
        method: 'post',
        contentType: 'application/json',
        payload: JSON.stringify(payload),
        muteHttpExceptions: true // Важливо для обробки помилок HTTP
      };

      const response = UrlFetchApp.fetch(url, options);
      const responseCode = response.getResponseCode();
      const responseDataText = response.getContentText();

      let responseData = {};
      try {
        responseData = JSON.parse(responseDataText);
      } catch (e) {
        Logger.log(`Не вдалося розпарсити відповідь від Telegram: ${responseDataText}`);
      }

      if (responseCode === 200 && responseData.ok) {
        Logger.log('Повідомлення Telegram відправлено успішно');
        return true;
      } else {
        const errorMessage = responseData.description || `HTTP Status Code: ${responseCode}, Response: ${responseDataText}`;
        Logger.log(`Спроба ${attempts + 1}/${MAX_RETRIES}: Помилка при відправці повідомлення: ${errorMessage}`);

        if (attempts + 1 < MAX_RETRIES) {
          Utilities.sleep(RETRY_DELAY_MS);
        }
      }
    } catch (error) {
      Logger.log(`Спроба ${attempts + 1}/${MAX_RETRIES}: Виняток при відправці повідомлення: ${error.toString()}`);
      if (attempts + 1 < MAX_RETRIES) {
        Utilities.sleep(RETRY_DELAY_MS);
      }
    }
    attempts++;
  }

  Logger.log(`Всі ${MAX_RETRIES} спроб відправки повідомлення для chatId ${chatId} були невдалими.`);
  return false;
}


// Унікальні константи для цього бота
const FUEL_DISCHARGE_BOT_TELEGRAM_TOKEN = '**********************************************'; // Chupik
const FUEL_DISCHARGE_BOT_CHAT_ID = "-**********";
// const FUEL_DISCHARGE_BOT_CHAT_ID = "276789696";
const FUEL_DISCHARGE_BOT_SPREADSHEET_ID = "1IsqVkWE0jGkfzNPiPYktxzIFCnDMF0i3IbgZcglQvt8";
const FUEL_DISCHARGE_BOT_SHEET_ID = "**********"; // Це GID аркуша, не назва
