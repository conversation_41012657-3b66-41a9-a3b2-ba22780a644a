function sortVMDtoPDF_processVMDs() {
  // Replace with your actual Sheet ID, Sheet Name, and Folder ID
  const sortVMD_SPREADSHEET_ID = '1IsqVkWE0jGkfzNPiPYktxzIFCnDMF0i3IbgZcglKvt8';
  const sortVMD_SHEET_ID = '**********'; // This is the gid, not the sheet name. You might need to get the sheet by name instead.
  const sortVMD_FOLDER_ID = '19DyLn22O9QK783HxoZGLIz4jilPvCNv0';

  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getSheetById(sortVMD_SHEET_ID); // Or use getSheetByName("Your Sheet Name")

  if (!sheet) {
    Logger.log('Sheet not found with ID: ' + sortVMD_SHEET_ID);
    return;
  }

  // Prompt user for date range
  const ui = SpreadsheetApp.getUi();
  const startDateResponse = ui.prompt('Введіть дату з (у форматі ДД.ММ.РРРР):');
  const endDateResponse = ui.prompt('Введіть дату по (у форматі ДД.ММ.РРРР):');

  const startDateParts = startDateResponse.getResponseText().split('.');
  const endDateParts = endDateResponse.getResponseText().split('.');

  if (startDateParts.length !== 3 || endDateParts.length !== 3) {
    ui.alert('Невірний формат дати. Будь ласка, використовуйте формат ДД.ММ.РРРР.');
    return;
  }

  // Note: Month is 0-indexed in JavaScript Date objects
  const startDate = new Date(startDateParts[2], startDateParts[1] - 1, startDateParts[0]);
  const endDate = new Date(endDateParts[2], endDateParts[1] - 1, endDateParts[0]);

  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    ui.alert('Невірний формат дати. Перевірте введені значення.');
    return;
  }

  const dataRange = sheet.getDataRange();
  const values = dataRange.getValues();

  // Assuming the header is in the first row
  const headerRow = values[0];
  const dateColumnIndex = headerRow.indexOf('Дата прибуття вантажу');
  const vmdColumnIndex = headerRow.indexOf('ВМД');

  if (dateColumnIndex === -1 || vmdColumnIndex === -1) {
    Logger.log('Не знайдено стовпці "Дата прибуття вантажу" або "ВМД". Перевірте назви стовпців.');
    ui.alert('Не знайдено стовпці "Дата прибуття вантажу" або "ВМД". Перевірте назви стовпців.');
    return;
  }

  const pdfUrls = [];

  // Iterate through rows, skipping the header
  // Iterate through rows, skipping the header
  for (let i = 1; i < values.length; i++) {
    const row = values[i];
    const dateValue = row[dateColumnIndex];
    const vmdLink = row[vmdColumnIndex];

    // Ensure dateValue is a Date object before comparison
    if (dateValue instanceof Date) {
      // Normalize dates to the start of the day for accurate comparison
      const rowDate = new Date(dateValue.getFullYear(), dateValue.getMonth(), dateValue.getDate());
      const start = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
      const end = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());

      if (rowDate >= start && rowDate <= end) {
        if (vmdLink && typeof vmdLink === 'string' && vmdLink.startsWith('http')) {
          pdfUrls.push(vmdLink);
        }
      }
    }
  }

  if (pdfUrls.length === 0) {
    ui.alert('Не знайдено ВМД за вказаний діапазон дат.');
    return;
  }

  Logger.log('Знайдено посилання на PDF: ' + pdfUrls.length);

  // --- PDF Fetching and Merging Logic ---
  // Google Apps Script has limitations in directly merging arbitrary PDFs from external URLs.
  // A common approach is to:
  // 1. Download each PDF from the URL to a temporary file in Google Drive using UrlFetchApp and DriveApp.
  // 2. Use a method to merge the temporary PDF files. This is the most complex part.
  //    - Google Apps Script does not have a built-in PDF merging function for arbitrary PDFs.
  //    - Potential solutions include:
  //      - Using a third-party API or service for PDF merging (requires external API calls and handling).
  //      - If the PDFs are Google Docs/Sheets/Slides, they can be exported as PDF and potentially combined, but this is not general for any PDF link.
  //      - A very complex script could potentially attempt to parse and combine PDF bytes, but this is highly unreliable and difficult.
  // 3. Save the final merged PDF to the specified folder.
  // 4. Clean up temporary files.

  // Given the complexity and limitations, the direct merging of arbitrary external PDFs is not straightforward in Google Apps Script.
  // The code below implements downloading PDFs to a temporary folder. The merging part still requires a more advanced solution.

  const tempFolder = DriveApp.createFolder('Temp_VMD_PDFs_' + new Date().getTime());
  const downloadedFiles = [];

  for (let i = 0; i < pdfUrls.length; i++) {
    const url = pdfUrls[i];
    let blob = null;
    // Generate sequential prefix for file naming
    const filePrefix = (i + 1).toString().padStart(2, '0') + '_';
    let fileName = filePrefix + 'downloaded_pdf_' + (i + 1) + '.pdf'; // Default file name with prefix

    try {
      if (url.includes('drive.google.com/file/d/')) {
        // Extract file ID from Google Drive URL
        const fileIdMatch = url.match(/google\.com\/file\/d\/([a-zA-Z0-9_-]+)/);
        if (fileIdMatch && fileIdMatch[1]) {
          const fileId = fileIdMatch[1];
          const driveFile = DriveApp.getFileById(fileId);
          blob = driveFile.getBlob();
          fileName = filePrefix + driveFile.getName(); // Use original file name from Drive with prefix
          Logger.log('Завантажено з Google Drive: ' + url);
        } else {
          throw new Error('Не вдалося отримати ID файлу з посилання Google Drive.');
        }
      } else {
        // Fetch from external URL
        const response = UrlFetchApp.fetch(url);
        blob = response.getBlob();
        // Attempt to get file name from headers or URL
        const contentDisposition = response.getHeaders()['Content-Disposition'];
        if (contentDisposition) {
          const fileNameMatch = contentDisposition.match(/filename="(.+)"/);
          if (fileNameMatch && fileNameMatch[1]) {
            fileName = filePrefix + fileNameMatch[1]; // Add prefix to extracted name
          } else {
             fileName = filePrefix + 'downloaded_pdf_' + (i + 1) + '.pdf'; // Fallback with prefix
          }
        } else {
           const urlParts = url.split('/');
           const baseFileName = urlParts[urlParts.length - 1].split('?')[0];
           fileName = filePrefix + (baseFileName || 'downloaded_pdf_' + (i + 1) + '.pdf'); // Add prefix
        }
        Logger.log('Завантажено з зовнішнього URL: ' + url);
      }

      if (blob) {
        const file = tempFolder.createFile(blob.setName(fileName));
        downloadedFiles.push(file);
      }

    } catch (e) {
      Logger.log('Помилка завантаження ' + url + ': ' + e.toString());
      ui.alert('Помилка завантаження одного з PDF: ' + url + '. Перевірте лог.');
    }
  }

  if (downloadedFiles.length === 0) {
    ui.alert('Не вдалося завантажити жоден PDF.');
    tempFolder.setTrashed(true); // Clean up empty temp folder
    return;
  }

  Logger.log('Завантажено PDF файлів: ' + downloadedFiles.length);

  // --- PDF Merging Logic (Placeholder) ---
  // Google Apps Script does NOT have a built-in function to merge arbitrary PDF files (blobs).
  // To merge the 'downloadedFiles' (which are Google Drive File objects), you would typically need to:
  // 1. Use a third-party PDF merging service/API. This would involve sending the downloaded file blobs to the external service and receiving a merged PDF blob back. (Requires setting up API calls and handling responses).
  // 2. If the PDFs are Google Docs/Sheets/Slides, you could potentially export them as PDF and then combine, but this is not a general solution for all PDF types.
  // 3. Attempt a very complex and often unreliable method of reading and combining the binary data of the PDF blobs within Apps Script. This is generally not recommended due to the complexity of the PDF format.

  // The 'downloadedFiles' array contains Google Drive File objects for the downloaded PDFs.
  // You would need to pass these files to your chosen merging method.

  // Example of how you *would* use a hypothetical merging function (this function needs to be implemented separately, likely using an external service):
  // const mergedPdfBlob = sortVMDtoPDF_mergePdfBlobs(downloadedFiles);

  const mergedPdfBlob = null; // Currently, mergedPdfBlob is null as merging logic is NOT implemented here.

  if (mergedPdfBlob) {
    const destinationFolder = DriveApp.getFolderById(sortVMDtoPDF_FOLDER_ID);
    destinationFolder.createFile(mergedPdfBlob.setName('Об\'єднані ВМД_' + new Date().toISOString() + '.pdf'));
    ui.alert('PDF файли успішно об\'єднано та збережено.');
  } else {
    // Updated alert to be more explicit about the merging requirement
    ui.alert('PDF файли завантажено до тимчасової папки на Диску (' + tempFolder.getUrl() + '). Об\'єднання PDF вимагає додаткової реалізації за допомогою стороннього сервісу або складнішого коду.');
  }

  // Clean up temporary folder (optional, but recommended)
  // Be cautious with automatic deletion, ensure you have the merged file first!
  // tempFolder.setTrashed(true);
}

// Placeholder for a hypothetical PDF merging function (requires external implementation)
// This function is NOT implemented in this script and serves only as an example of how you might call a merging process.
// It would take an array of Google Drive File objects and return a single merged PDF blob.
// function sortVMDtoPDF_mergePdfBlobs(files) {
//   // Implementation using a third-party service or complex custom logic goes here.
//   // This is a complex task in Google Apps Script.
//   Logger.log('Placeholder: Calling PDF merging function with ' + files.length + ' files.');
//   return null; // Return the merged blob if successful, otherwise null.
// }
