function fillSigners() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  
  // Отримуємо аркуші "Контрагенти" та "Реєстр договорів"
  const contractorsSheet = ss.getSheetByName("Контрагенти");
  const contractsSheet = ss.getSheetByName("Реєстр договорів");
  
  // Отримуємо значення зі стовпців
  const contractorsData = contractorsSheet.getRange("A2:A" + contractorsSheet.getLastRow()).getValues();
  const contractsData = contractsSheet.getRange("A2:E" + contractsSheet.getLastRow()).getValues();
  
  // Проходимо по кожному контрагенту в аркуші "контрагенти"
  contractorsData.forEach((contractor, i) => {
    const contractorName = contractor[0];
    let signer = "";

    // Шукаємо перший збіг скороченої назви в "Реєстрі договорів"
    for (let j = 0; j < contractsData.length; j++) {
      if (contractsData[j][0] === contractorName) {
        signer = contractsData[j][4]; // Стовпець E з підписантом
        break;
      }
    }

    // Записуємо знайденого підписанта в стовпець F відповідного рядка в "контрагенти"
    if (signer) {
      contractorsSheet.getRange(i + 2, 6).setValue(signer); // Стовпець F
    }
  });
}
