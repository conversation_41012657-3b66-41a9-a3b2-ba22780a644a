// function removeStampsAndSignatures() {
//   var folderId = '1eqFKhSuNQIc8DEE2CHsUStH71DhC4Oed'; // ID папки
//   var folder = DriveApp.getFolderById(folderId);
//   var files = folder.getFilesByType(MimeType.GOOGLE_DOCS); // Отримуємо всі Google документи в папці

//   // Проходимо по кожному файлу
//   while (files.hasNext()) {
//     var file = files.next();
//     var doc = DocumentApp.openById(file.getId()); // Відкриваємо документ
//     var body = doc.getBody();
    
//     // Логування для відлагодження
//     Logger.log('Обробка документу: ' + file.getName());

//     // Перевіряємо зображення в документі
//     var images = body.getImages();
//     Logger.log('Кількість зображень: ' + images.length);
    
//     for (var i = 0; i < images.length; i++) {
//       var image = images[i];
//       Logger.log('Зображення знайдено та видалено.');
//       body.deleteImage(image); // Видаляємо зображення
//     }

//     // Перевіряємо всі параграфи для текстових підписів чи печаток
//     var paragraphs = body.getParagraphs();
//     for (var j = 0; j < paragraphs.length; j++) {
//       var para = paragraphs[j];
//       var text = para.getText().toLowerCase();

//       // Перевіряємо наявність підпису або печатки
//       if (text.includes("підпис") || text.includes("печатка") || text.includes("підписаний")) {
//         Logger.log('Текст з "підпис" чи "печатка" знайдений і видалений.');
//         body.removeChild(para); // Видаляємо параграф
//       }
//     }

//     // Зберігаємо зміни
//     doc.saveAndClose();
//   }

//   // Показуємо всі логи після виконання
//   Logger.flush();
// }
