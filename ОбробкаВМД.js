// Константи для ID документів
const vmd_VMD_FOLDER_ID = '19DyLn22O9QK783HxoZGLIz4jilPvCNv0'; // ID папки для зберігання ВМД
const vmd_SPREADSHEET_ID = '1IsqVkWE0jGkfzNPiPYktxzIFCnDMF0i3IbgZcglQvt8'; // ID таблиці
const vmd_SHEET_NAME = 'Розмитнення'; // Назва аркуша
const vmd_SENDER_EMAIL = '<EMAIL>'; // Email відправника

/**
 * Отримання часу останньої перевірки пошти
 */
function vmd_getLastCheckedTime() {
  const props = PropertiesService.getScriptProperties();
  const lastCheckedTimeStr = props.getProperty('vmd_lastCheckedTime');
  return lastCheckedTimeStr ? new Date(lastCheckedTimeStr) : new Date(0); // Якщо немає, повертаємо найстаріший можливий час
}

/**
 * Збереження часу останньої перевірки пошти
 */
function vmd_saveLastCheckedTime(date) {
  const props = PropertiesService.getScriptProperties();
  props.setProperty('vmd_lastCheckedTime', date.toISOString());
}

/**
 * Отримання списку оброблених повідомлень
 */
function vmd_getProcessedMessageIds() {
  const props = PropertiesService.getScriptProperties();
  const processedIdsStr = props.getProperty('vmd_processedMessageIds');
  return processedIdsStr ? JSON.parse(processedIdsStr) : [];
}

/**
 * Додавання ID повідомлення до списку оброблених
 */
function vmd_addToProcessedMessageIds(messageId) {
  const props = PropertiesService.getScriptProperties();
  const processedIds = vmd_getProcessedMessageIds();
  
  // Додаємо новий ID, обмежуємо масив останніми 100 ID для економії пам'яті
  processedIds.push(messageId);
  if (processedIds.length > 100) {
    processedIds.shift(); // Видаляємо найстаріший ID
  }
  
  props.setProperty('vmd_processedMessageIds', JSON.stringify(processedIds));
}

/**
 * Перевірка чи було повідомлення оброблено раніше
 */
function vmd_isMessageProcessed(messageId) {
  const processedIds = vmd_getProcessedMessageIds();
  return processedIds.includes(messageId);
}

/**
 * Функція яка відкриває pdf файл в Google Docs
 */
function vmd_openPdfInDocs(pdfFile) {
  try {
    Logger.log('[ВМД] Початок конвертації файлу: ' + pdfFile.getName());

    // Конвертуємо PDF в Google Docs
    const docFile = Drive.Files.copy({
      title: pdfFile.getName().replace('.pdf', '') + '_converted',
      mimeType: MimeType.GOOGLE_DOCS
    }, pdfFile.getId(), { supportsAllDrives: true }); // Додано supportsAllDrives

    Logger.log('[ВМД] Файл успішно конвертовано в Google Docs');

    // Отримуємо текст з документа
    const doc = DocumentApp.openById(docFile.id);
    const text = doc.getBody().getText();

    // Видаляємо тимчасовий Google Doc файл
    try {
      DriveApp.getFileById(docFile.id).setTrashed(true);
      Logger.log('[ВМД] Тимчасовий Google Doc файл видалено: ' + docFile.id);
    } catch (e) {
      Logger.log('[ВМД] Помилка при видаленні тимчасового Google Doc файлу: ' + e.toString());
    }

    Logger.log('[ВМД] Отримано текст з документа');
    return text;

  } catch (error) {
    Logger.log('[ВМД] Помилка при конвертації в Google Docs: ' + error.toString());
    // Спробуємо видалити файл, якщо він був створений, але сталася помилка
    if (typeof docFile !== 'undefined' && docFile && docFile.id) {
      try {
        DriveApp.getFileById(docFile.id).setTrashed(true);
        Logger.log('[ВМД] Тимчасовий Google Doc файл видалено після помилки: ' + docFile.id);
      } catch (e) {
        Logger.log('[ВМД] Помилка при видаленні тимчасового Google Doc файлу після помилки: ' + e.toString());
      }
    }
    return ''; // Повертаємо порожній рядок у разі помилки
  }
}

/**
 * Функція для витягнення даних з тексту ВМД
 */
function vmd_parseVmdText(textContent) {
  const vmdData = {};

  try {
    // 1. Номер ВМД
    const vmdNumberMatch = textContent.match(/до ВМД №\s*([A-Z0-9]+U\d+)/);
    if (vmdNumberMatch && vmdNumberMatch[1]) {
      vmdData.vmdNumber = vmdNumberMatch[1].trim();
      Logger.log('[ВМД] Знайдено номер ВМД: ' + vmdData.vmdNumber);
    } else {
      Logger.log('[ВМД] Не вдалося знайти номер ВМД.');
    }    // 2. Митна вартість грн.    // Спочатку шукаємо в графі 12 "Відомості про вартість"
    let customsValueMatch = textContent.match(/12\s+Відомості\s+про\s+вартість\s+([\d,]+(?:\.\d+)?)/);
    
    // Якщо не знайшли в графі 12, шукаємо в блоці 47
    if (!customsValueMatch) {
      customsValueMatch = textContent.match(/47[\s\S]*?Основа\s+нарахування[\s\S]*?(?:А00|[A-ZА-Я]\d{2})\s+([\d,]+(?:\.\d+)?)/);
    }
    
    if (customsValueMatch && customsValueMatch[1]) {
      vmdData.customsValueUAH = parseFloat(customsValueMatch[1].replace(',', '.'));
      Logger.log('[ВМД] Знайдено митну вартість грн.: ' + vmdData.customsValueUAH);
    } else {
      Logger.log('[ВМД] Не вдалося знайти митну вартість грн.');
    }

    // 3. Обмінний курс
    const exchangeRateMatch = textContent.match(/23 Обмінний курс\s*([\d.]+)/);
    if (exchangeRateMatch && exchangeRateMatch[1]) {
      vmdData.exchangeRate = parseFloat(exchangeRateMatch[1].replace(',', '.'));
      Logger.log('[ВМД] Знайдено обмінний курс: ' + vmdData.exchangeRate);
    } else {
      Logger.log('[ВМД] Не вдалося знайти обмінний курс.');
    }

  } catch (error) {
    Logger.log('[ВМД] Помилка при парсингу тексту ВМД: ' + error.toString());
  }

  return vmdData;
}


/**
 * Головна функція для перевірки пошти на наявність ВМД
 */
function vmd_checkEmailsForVMD() {
  try {
    // Отримуємо час останньої перевірки
    const lastCheckedTime = vmd_getLastCheckedTime();
    const now = new Date();
    
    // Формуємо пошуковий запит
    const query = `from:${vmd_SENDER_EMAIL} after:${vmd_formatDateForQuery(lastCheckedTime)} has:attachment`;
    const threads = GmailApp.search(query, 0, 50); // Збільшуємо ліміт до 50 для більшої надійності
    
    if (threads.length === 0) {
      Logger.log('[ВМД] Нових повідомлень від брокера не знайдено');
      return;
    }
    
    Logger.log(`[ВМД] Знайдено ${threads.length} потенційних потоків повідомлень від брокера`);
    let processedCount = 0;
    
    // Обробляємо кожен потік повідомлень
    for (let i = 0; i < threads.length; i++) {
      const messages = threads[i].getMessages();
      
      for (let j = 0; j < messages.length; j++) {
        const message = messages[j];
        const messageId = message.getId();
        const messageDate = message.getDate();
        
        // Пропускаємо повідомлення, які вже були оброблені
        if (vmd_isMessageProcessed(messageId)) {
          Logger.log(`[ВМД] Пропускаємо вже оброблене повідомлення: ${messageId}`);
          continue;
        }
        
        // Перевіряємо, чи це дійсно новий лист (після останньої перевірки)
        if (messageDate > lastCheckedTime) {
          const from = message.getFrom();
          
          // Додаткова перевірка відправника для надійності
          if (from.includes(vmd_SENDER_EMAIL)) {
            const attachments = message.getAttachments();
            
            // Перевіряємо кожен вкладений файл
            for (let k = 0; k < attachments.length; k++) {
              const attachment = attachments[k];
              const fileName = attachment.getName();
              
              // Перевіряємо, чи це файл ВМД (має формат "вага-номер.pdf")
              if (fileName.endsWith('.pdf') && fileName.includes('-')) {
                vmd_processVMDFile(attachment);
                processedCount++;
              }
            }
            
            // Додаємо повідомлення до оброблених
            vmd_addToProcessedMessageIds(messageId);
          }
        }
      }
    }
    
    // Оновлюємо час останньої перевірки
    vmd_saveLastCheckedTime(now);
    
    Logger.log(`[ВМД] Оброблено ${processedCount} нових вкладень ВМД`);
    
  } catch (error) {
    Logger.log(`[ВМД] Помилка при перевірці пошти: ${error.toString()}`);
  }
}

/**
 * Функція для обробки файлу ВМД
 */
function vmd_processVMDFile(attachment) {
  try {
    const fileName = attachment.getName();
    Logger.log(`[ВМД] Обробка файлу ВМД: ${fileName}`);
    
    // Витягуємо вагу та номер машини з назви файлу
    const parts = fileName.split('.')[0].split('-');
    if (parts.length !== 2) {
      Logger.log(`[ВМД] Неправильний формат назви файлу: ${fileName}`);
      return;
    }
    
    const weight = parseInt(parts[0], 10);
    const vehicleNumber = parts[1];
    
    if (isNaN(weight) || !vehicleNumber) {
      Logger.log(`[ВМД] Не вдалося витягнути вагу або номер машини з назви файлу: ${fileName}`);
      return;
    }
    
    Logger.log(`[ВМД] Витягнуто вагу: ${weight}, номер машини: ${vehicleNumber}`);
    
    // Зберігаємо файл на Google Drive
    const folder = DriveApp.getFolderById(vmd_VMD_FOLDER_ID);
    const file = folder.createFile(attachment);
    const fileUrl = file.getUrl();
    
    Logger.log(`[ВМД] Файл збережено на Google Drive: ${fileUrl}`);

    // Конвертуємо PDF в текст
    const vmdTextContent = vmd_openPdfInDocs(file);

    if (!vmdTextContent) {
      Logger.log(`[ВМД] Не вдалося отримати текст з файлу: ${fileName}`);
      // Можливо, варто додати логіку для обробки цієї помилки, наприклад, позначити рядок як помилковий
      return; // Зупиняємо обробку цього файлу
    }

    // Шукаємо відповідний рядок у таблиці та оновлюємо його
    vmd_updateSpreadsheetWithVMD(weight, vehicleNumber, fileUrl, vmdTextContent);

  } catch (error) {
    Logger.log(`[ВМД] Помилка при обробці файлу ВМД: ${error.toString()}`);
  }
}

/**
 * Функція для оновлення таблиці з даними ВМД (посилання, номер, вартість, курс)
 */
function vmd_updateSpreadsheetWithVMD(weight, vehicleNumber, fileUrl, vmdTextContent) {
  try {
    // Відкриваємо таблицю
    const spreadsheet = SpreadsheetApp.openById(vmd_SPREADSHEET_ID);
    const sheet = spreadsheet.getSheetByName(vmd_SHEET_NAME);
    
    if (!sheet) {
      Logger.log(`[ВМД] Аркуш з назвою ${vmd_SHEET_NAME} не знайдено`);
      return;
    }
    
    // Отримуємо всі дані з таблиці
    const data = sheet.getDataRange().getValues();
    const headers = data[0];
    
    // Знаходимо індекси потрібних стовпців
    const vehicleColumnIndex = headers.indexOf('Номер автомобіля і причепа'); // Колонка для пошуку рядка
    const weightColumnIndex = headers.indexOf('Маса відвантаження кг.'); // Колонка для пошуку рядка
    const vmdLinkColumnIndex = headers.indexOf('ВМД'); // Колонка для посилання на PDF
    const vmdNumberColumnIndex = headers.indexOf('№ВМД'); // Нова колонка
    const customsValueColumnIndex = headers.indexOf('Митна вартість грн.'); // Нова колонка
    const exchangeRateColumnIndex = headers.indexOf('Обмінний курс'); // Нова колонка

    // Перевірка наявності всіх необхідних стовпців
    if (vehicleColumnIndex === -1 || weightColumnIndex === -1 || vmdLinkColumnIndex === -1 ||
        vmdNumberColumnIndex === -1 || customsValueColumnIndex === -1 || exchangeRateColumnIndex === -1) {
      Logger.log('[ВМД] Не знайдено один або більше необхідних стовпців у таблиці. Перевірте назви: ' +
                 '"Номер автомобіля і причепа", "Маса відвантаження кг.", "ВМД", "№ВМД", "Митна вартість грн.", "Обмінний курс"');
      return;
    }
    
    // Шукаємо відповідний рядок, але починаємо з кінця таблиці (нові записи)
    let matchingRows = [];
    
    for (let i = data.length - 1; i >= 1; i--) {
      const rowVehicleNumbers = data[i][vehicleColumnIndex].toString();
      const rowWeight = parseInt(data[i][weightColumnIndex], 10);
      
      // Перевіряємо, чи містить рядок з номерами машин наш номер
      if (rowVehicleNumbers.includes(vehicleNumber)) {
        // Додаємо дані рядка та його індекс для подальшого аналізу
        matchingRows.push({
          index: i,
          vehicleNumbers: rowVehicleNumbers,
          weight: rowWeight,
          vmdLinkValue: data[i][vmdLinkColumnIndex], // Перевіряємо посилання
          vmdNumberValue: data[i][vmdNumberColumnIndex] // Перевіряємо чи вже є номер ВМД
        });
      }
    }
    
    Logger.log(`[ВМД] Знайдено ${matchingRows.length} рядків з номером машини ${vehicleNumber}`);
    
    if (matchingRows.length === 0) {
      Logger.log(`[ВМД] Не знайдено рядків для номера машини ${vehicleNumber}`);
      return;
    }
    
    // Фільтруємо рядки де співпадає вага
    const matchingWeightRows = matchingRows.filter(row => row.weight === weight);
    
    if (matchingWeightRows.length === 0) {
      Logger.log(`[ВМД] Знайдено рядки з номером машини ${vehicleNumber}, але вага не співпадає. Очікувана вага: ${weight}`);
      return;
    }
    
    // Сортуємо за індексом (від більшого до меншого, тобто від нових до старих)
    matchingWeightRows.sort((a, b) => b.index - a.index);
    
    // Беремо найновіший запис
    const selectedRow = matchingWeightRows[0];
    
    // Перевіряємо, чи дані ВМД (номер або посилання) вже не встановлено
    if (!selectedRow.vmdLinkValue && !selectedRow.vmdNumberValue) {
      // Парсимо текст ВМД
      const parsedData = vmd_parseVmdText(vmdTextContent);

      // Готуємо дані для оновлення рядка
      const updates = {};
      updates[vmdLinkColumnIndex] = fileUrl; // Посилання на PDF
      if (parsedData.vmdNumber) updates[vmdNumberColumnIndex] = parsedData.vmdNumber;
      if (parsedData.customsValueUAH) updates[customsValueColumnIndex] = parsedData.customsValueUAH;
      if (parsedData.exchangeRate) updates[exchangeRateColumnIndex] = parsedData.exchangeRate;

      // Оновлюємо комірки в знайденому рядку
      const rowRange = sheet.getRange(selectedRow.index + 1, 1, 1, headers.length);
      const rowValues = rowRange.getValues()[0];

      // Застосовуємо оновлення
      for (const colIndex in updates) {
        rowValues[colIndex] = updates[colIndex];
      }

      // Записуємо оновлений рядок
      rowRange.setValues([rowValues]);

      Logger.log(`[ВМД] Оновлено дані ВМД у рядку ${selectedRow.index + 1} (${selectedRow.vehicleNumbers}, вага: ${selectedRow.weight}): ` + JSON.stringify(parsedData));

      // Додаткове логування перед генерацією XML (якщо потрібно, можна додати нові дані)
      Logger.log(`[ВМД] Передача даних в BAS Import - Номер рядка: ${selectedRow.index + 1}, Дані рядка: ${JSON.stringify({
        vehicleNumbers: selectedRow.vehicleNumbers,
        weight: selectedRow.weight,
        vmdUrl: fileUrl,
        vmdNumber: parsedData.vmdNumber,
        customsValue: parsedData.customsValueUAH,
        exchangeRate: parsedData.exchangeRate
      })}`);

      // Генеруємо XML для надходження товарів
      createGoodsReceiptXmlFile(selectedRow.index + 1);
      Logger.log(`[ВМД] Запущено генерацію XML для рядка ${selectedRow.index + 1}`);
      // Генеруємо XML для митної дклараці
      createCustomsDeclarationXmlFile(selectedRow.index + 1);
      Logger.log(`[ВМД] Запущено генерацію XML для митної дкларації для рядка ${selectedRow.index + 1}`);
      // Генеруємо XML для переміщення 1
      createFirstMovementXmlFile(selectedRow.index + 1);
      Logger.log(`[ВМД] Запущено генерацію XML для переміщення 1 для рядка ${selectedRow.index + 1}`);
      // Генеруємо XML для переміщення 2
      createSecondMovementXmlFile(selectedRow.index + 1);
      Logger.log(`[ВМД] Запущено генерацію XML для переміщення 2 для рядка ${selectedRow.index + 1}`);
      // Відправляємо сповіщення в Telegram
      try {
        // Викликаємо функцію з telegram_bot.js для відправки сповіщення
        tgBotMagistr_sendVMDNotification(vehicleNumber, weight);
        Logger.log(`[ВМД] Запущено відправку сповіщення в Telegram для авто ${vehicleNumber}`);
      } catch (telegramError) {
        Logger.log(`[ВМД] Помилка при спробі відправки сповіщення в Telegram: ${telegramError.toString()}`);
      }
    } else {
      Logger.log(`[ВМД] Дані ВМД (посилання або номер) вже встановлено для рядка ${selectedRow.index + 1}`);
    }

  } catch (error) {
    Logger.log(`[ВМД] Помилка при оновленні таблиці: ${error.toString()}`);
  }
}

/**
 * Допоміжна функція для форматування дати для пошукового запиту Gmail
 */
function vmd_formatDateForQuery(date) {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  
  return `${year}/${month}/${day}`;
}

/**
 * ТЕСТОВА ФУНКЦІЯ: Обробляє останній знайдений ВМД файл з пошти.
 * Можна запустити вручну для перевірки роботи основного функціоналу.
 */
function vmd_testLatestVMDProcessing() {
  try {
    Logger.log('=== ЗАПУСК ТЕСТОВОЇ ОБРОБКИ ОСТАННІХ 10 ВМД ===');

    // Шукаємо останні 10 листів від відправника з PDF вкладенням
    const query = `from:${vmd_SENDER_EMAIL} has:attachment filename:pdf`;
    const threads = GmailApp.search(query, 0, 10); // Отримуємо 10 останніх потоків
    
    if (threads.length === 0) {
      Logger.log('[ВМД-ТЕСТ] Не знайдено листів від брокера з PDF вкладеннями.');
      return;
    }

    Logger.log(`[ВМД-ТЕСТ] Знайдено ${threads.length} потоків повідомлень.`);
    let processedCount = 0;

    // Обробляємо кожен потік
    for (const thread of threads) {
      const messages = thread.getMessages();
      const latestMessage = messages[messages.length - 1]; // Беремо останнє повідомлення в потоці

      if (!latestMessage) {
        continue;
      }

      const attachments = latestMessage.getAttachments();

      // Обробляємо всі PDF вкладення в повідомленні
      for (const attachment of attachments) {
        const fileName = attachment.getName();
        if (fileName.endsWith('.pdf') && fileName.includes('-')) {
          Logger.log(`[ВМД-ТЕСТ] Обробка ВМД вкладення: ${fileName}`);
          vmd_processVMDFile(attachment);
          processedCount++;
        }
      }
    }

    Logger.log(`[ВМД-ТЕСТ] Всього оброблено ${processedCount} ВМД файлів`);
    Logger.log('=== ТЕСТОВА ОБРОБКА ОСТАННІХ ВМД ЗАВЕРШЕНА ===');

  } catch (error) {
    Logger.log(`[ВМД-ТЕСТ] Помилка під час тестової обробки: ${error.toString()}`);
  }
}
