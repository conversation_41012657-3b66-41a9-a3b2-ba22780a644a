// --- ГОЛОВНИЙ ПРОЦЕС ОБРОБКИ ДОКУМЕНТІВ (RMPD, CMR, ІНВОЙС) ---

/**
 * Головна функція, яка запускає всі етапи обробки документів (RMPD, CMR, Інвойс) в правильному порядку.
 * Рекомендується налаштувати тригер для автоматичного запуску цієї функції (наприклад, щогодини).
 */
function runCompleteDocumentProcess() {
  var lock = LockService.getScriptLock();
  try {
    lock.waitLock(120000); // Очікування до 2 хвилин
    Logger.log('=== ПОЧАТОК ПОВНОГО ПРОЦЕСУ ОБРОБКИ ДОКУМЕНТІВ ===');

    // ЕТАП 1: Пошук і обробка підтверджень RMPD
    Logger.log('--- ЕТАП 1: Пошук і обробка підтверджень RMPD ---');
    if (typeof processRmpdConfirmations === 'function') {
      processRmpdConfirmations();
      Logger.log('Етап 1 успішно завершено: Підтвердження RMPD оброблені');
    } else {
      Logger.log('ПОМИЛКА: Функція processRmpdConfirmations не знайдена. Перевірте, чи файл "Читаєм RMPD.js" включено в проект.');
    }

    // ЕТАП 2: Моніторинг нових машин Orlen
    Logger.log('--- ЕТАП 2: Моніторинг нових машин Orlen ---');
    if (typeof orlenMonMain === 'function') {
      orlenMonMain();
      Logger.log('Етап 2 успішно завершено: Orlen моніторинг виконано');
    } else {
      Logger.log('ПОМИЛКА: Функція orlenMonMain не знайдена. Перевірте, чи файл "парсер орлен.js" включено в проект.');
    }

    // ЕТАП 3: Пошук і обробка CMR та Інвойсів
    Logger.log('--- ЕТАП 3: Пошук і обробка CMR та Інвойсів ---');
    if (typeof processNewEmails === 'function') {
      processNewEmails();
      Logger.log('Етап 3 успішно завершено: CMR та Інвойси оброблені');
    } else {
      Logger.log('ПОМИЛКА: Функція processNewEmails не знайдена. Перевірте, чи файл "Читаємо з пошти PDF CMR і Invoise від 04.04.2025.js" включено в проект.');
    }

    // ЕТАП 4: Перенесення і відправка зворотніх RMPD
    Logger.log('--- ЕТАП 4: Перенесення і відправка зворотніх RMPD ---');
    if (typeof rmpdTr_transferRmpdNumbers === 'function') {
      rmpdTr_transferRmpdNumbers();
      Logger.log('Етап 4 успішно завершено: Зворотні RMPD перенесені і відправлені');
    } else {
      Logger.log('ПОМИЛКА: Функція rmpdTr_transferRmpdNumbers не знайдена. Перевірте, чи файл "transferRmpdData.js" включено в проект.');
    }

    // ЕТАП 5: Перевірка та відправка документів актів
    Logger.log('--- ЕТАП 5: Перевірка та відправка документів ---');
    if (typeof checkAndSendDocumentsTrigger === 'function') {
      checkAndSendDocumentsTrigger();
      Logger.log('Етап 5 успішно завершено: Документи перевірені та відправлені');
    } else {
      Logger.log('ПОМИЛКА: Функція checkAndSendDocumentsTrigger не знайдена. Перевірте, чи файл Заповнення Акту 01.04.2025.js включено в проект.');
    }

    // ЕТАП 6: Обробка ВМД
    Logger.log('--- ЕТАП 6: Обробка ВМД ---');
    if (typeof vmd_checkEmailsForVMD === 'function') {
      vmd_checkEmailsForVMD();
      Logger.log('Етап 6 успішно завершено: ВМД оброблені');
    } else {
      Logger.log('ПОМИЛКА: Функція vmd_checkEmailsForVMD не знайдена. Перевірте, чи файл ОбробкаВМД.js включено в проект.');
    }

    Logger.log('=== ПОВНИЙ ПРОЦЕС ОБРОБКИ ДОКУМЕНТІВ УСПІШНО ЗАВЕРШЕНО ===');

  } catch (error) {
    Logger.log('КРИТИЧНА ПОМИЛКА в runCompleteDocumentProcess: ' + error.toString() + "\n" + error.stack);
  } finally {
    lock.releaseLock();
  }
}

/**
 * Допоміжна функція для перевірки наявності всіх необхідних функцій.
 * Запускати вручну для діагностики.
 */
function checkDocumentProcessFunctionsAvailability() {
  Logger.log('Перевірка наявності необхідних функцій:');

  Logger.log('1. processRmpdConfirmations (Читаєм RMPD.js): ' +
             (typeof processRmpdConfirmations === 'function' ? 'ДОСТУПНА' : 'НЕ ЗНАЙДЕНА'));

  Logger.log('2. processNewEmails (Читаємо з пошти PDF CMR і Invoise від 04.04.2025.js): ' +
             (typeof processNewEmails === 'function' ? 'ДОСТУПНА' : 'НЕ ЗНАЙДЕНА'));

  Logger.log('3. rmpdTr_transferRmpdNumbers (transferRmpdData.js): ' +
             (typeof rmpdTr_transferRmpdNumbers === 'function' ? 'ДОСТУПНА' : 'НЕ ЗНАЙДЕНА'));

  Logger.log('4. checkAndSendDocumentsTrigger (Заповнення Акту 01.04.2025.js): ' +
             (typeof checkAndSendDocumentsTrigger === 'function' ? 'ДОСТУПНА' : 'НЕ ЗНАЙДЕНА'));

  Logger.log('5. vmd_checkEmailsForVMD (ОбробкаВМД.js): ' +
             (typeof vmd_checkEmailsForVMD === 'function' ? 'ДОСТУПНА' : 'НЕ ЗНАЙДЕНА'));
}
