# Progress

**Що працює:**
- Автоматичне створення та заповнення різноманітних технічних та фінансових документів.
- Інтеграція з Telegram Bot для надсилання документів та повідомлень.
- Парсинг даних з зовнішніх систем (Orlen) та поштових вкладень (PDF CMR, Invoise, RMPD підтвердження).
- Інтеграція з Google Sheets як основною базою даних.
- Інтеграція з BAS/Oracle для генерації XML файлів.
- Моніторинг GPS та ETA вантажівок.
- Логіка автоматичного визначення часу розвантаження з урахуванням зайнятості резервуарів та груп.
- Виправлено помилку повторного використання резервуарів шляхом додавання виклику `removeTank`.
- Враховано наявність двох паралельних насосів для дизеля при розрахунку часу зливу.
- **Виправлено проблему перезапису резервуару:**
    - Ручне введення резервуару тепер зберігається, якщо автоматичний вибір не активований.
    - Автоматичний вибір резервуару та його видалення зі списку вільних відбувається лише за умови явного вибору автоматичного режиму.
- **Виправлено помилку повторного оголошення змінної `selectedTank`:** Змінено `let selectedTank` на `selectedTank =` у функції `createResponseDocument` для уникнення повторного оголошення.

**Що залишилося побудувати/зробити:**
- Комплексне автоматизоване тестування для виявлення потенційних помилок на ранніх етапах.
- Покращення механізмів обробки помилок та логування для кращої діагностики проблем.
- Перегляд та покращення безпеки зберігання ключів API.
- Оптимізація залежності від Google Services для потенційного підвищення масштабованості або переносимості.

**Поточний статус:**
- Основна функціональність автоматизації документообігу та управління резервуарами працює.
- Критичні помилки, пов'язані з вибором резервуарів та часом розвантаження, виправлені.
- Помилка повторного оголошення змінної `selectedTank` виправлена.
- Код готовий до тестування користувачем для підтвердження виправлень.

**Відомі проблеми:**
- Проблеми з дозволами Google Apps Script (Exception: Unexpected error while getting the method or property openById on object SpreadsheetApp.) можуть виникати через некоректні дозволи або ID таблиці/аркуша.
- Ключі API наразі зберігаються безпосередньо в коді.

**Еволюція проектних рішень:**
- Початкова логіка вибору резервуарів була покращена для врахування груп та паралельних насосів, що дозволило оптимізувати процес зливу палива.
- Виявлення та виправлення помилки з `removeTank` значно підвищило надійність системи.
- Перехід до більш гнучкої логіки вибору резервуарів, яка враховує як автоматичний, так і ручний ввід, покращив користувацький досвід.
- Виправлення помилки повторного оголошення змінної `selectedTank` забезпечило коректну роботу коду.
