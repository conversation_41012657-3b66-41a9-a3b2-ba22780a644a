// Константи
const BAS_IMPORT_SHEET_ID = '1IsqVkWE0jGkfzNPiPYktxzIFCnDMF0i3IbgZcglQvt8'; // ID таблиці
const BAS_IMPORT_SHEET_NAME = 'Розмитнення'; // Назва аркуша
const BAS_IMPORT_XML_FOLDER_ID = '13SjfIUX85P_h2rVbi9AX1FnFzViQmJ-4'; // ID папки для збереження XML

/**
 * Генерує XML-файл для імпорту в BAS на основі даних з рядка аркуша "Розмитнення".
 * @param {number} rowIndex Номер рядка (починаючи з 1) в аркуші "Розмитнення".
 */
function createGoodsReceiptXmlFile(rowIndex) {
  try {
    Logger.log(`[BAS Import] Початок генерації XML для рядка: ${rowIndex}`);

    // Перевірка вхідного параметра
    if (!rowIndex || typeof rowIndex !== 'number' || rowIndex < 2) {
      Logger.log('[BAS Import] Неправильний номер рядка. Має бути число більше 1.');
      return;
    }

    // Відкриваємо аркуш "Розмитнення"
    const spreadsheet = SpreadsheetApp.openById(BAS_IMPORT_SHEET_ID);
    const sheet = spreadsheet.getSheetByName(BAS_IMPORT_SHEET_NAME);
    if (!sheet) {
      Logger.log(`[BAS Import] Аркуш з назвою "${BAS_IMPORT_SHEET_NAME}" не знайдено.`);
      return;
    }

    // Отримуємо заголовки та дані з вказаного рядка
    const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
    const rowDataValues = sheet.getRange(rowIndex, 1, 1, sheet.getLastColumn()).getValues()[0];
    const rowData = {};
    headers.forEach((header, index) => {
      rowData[header] = rowDataValues[index];
    });

    // --- Отримання необхідних даних з rowData ---
    const requiredColumns = [
      'Маса відвантаження кг.',
      'Найменування вантажу',
      'Сума $',
      "Об'єм при 15°C (л)"
    ];

    for (const col of requiredColumns) {
      if (!(col in rowData) || rowData[col] === '') {
        Logger.log(`[BAS Import] Відсутні дані в колонці "${col}" для рядка ${rowIndex}. Генерація XML неможлива.`);
        return;
      }
    }

    const quantityKg = rowData['Маса відвантаження кг.'];
    const itemName = rowData['Найменування вантажу'];
    const totalAmount = rowData['Сума $'];
    const exciseLitres15C = rowData["Об'єм при 15°C (л)"];

    // Отримуємо дату і час з аркуша з стовпця Дата інвойсу в форматі dd.MM.yyyy hh:mm:ss
    const dateInvoice = rowData['Дата інвойсу'];
    const timestamp = Utilities.formatDate(new Date(dateInvoice), Session.getScriptTimeZone(), "yyyyMMddHHmmss");
    const formattedDateTime = Utilities.formatDate(new Date(dateInvoice), Session.getScriptTimeZone(), "dd.MM.yyyy HH:mm:ss");

    // Генеруємо унікальний номер документа для XML
    const docNumber = 'Надходження_товарів_${timestamp}';
    // --- Побудова XML ---
    const xmlRoot = XmlService.createElement('GoodsReceipt');

    // --- Секція Header ---
    const header = XmlService.createElement('Header');
    header.addContent(XmlService.createElement('DocType').setText('Надходження_товарів'));
    header.addContent(XmlService.createElement('DocNumber').setText(docNumber));
    header.addContent(XmlService.createElement('від').setText(formattedDateTime));
    header.addContent(XmlService.createElement('Організація').setText('МАГІСТР-Д ТОВ'));
    header.addContent(XmlService.createElement('Постачальник').setText('ОРЛЕН Летува'));
    header.addContent(XmlService.createElement('Договір').setText('контракт002'));
    header.addContent(XmlService.createElement('Склад').setText('Товари в дорозі'));
    xmlRoot.addContent(header);

    // --- Секція Additional ---
    const additional = XmlService.createElement('Additional');
    additional.addContent(XmlService.createElement('Менеджер').setText('Михайло'));
    additional.addContent(XmlService.createElement('Підрозділ').setText('Основна діяльність'));
    additional.addContent(XmlService.createElement('Порядок_розрахунків').setText('По договорах'));
    additional.addContent(XmlService.createElement('Оплата').setText('Будь-яка'));
    additional.addContent(XmlService.createElement('Рахунок_органзації').setText('9968, КРЕДОБАНК, USD'));
    additional.addContent(XmlService.createElement('Керівник').setText('Джумага О.М.'));
    additional.addContent(XmlService.createElement('Рахунок_контрагента').setText('(USD)'));
    additional.addContent(XmlService.createElement('Вантаж_прийняв').setText('Яворський Михайло Зіновійович'));
    additional.addContent(XmlService.createElement('Валюта_взаєморозрахунків').setText('USD'));
    additional.addContent(XmlService.createElement('Операція').setText('Імпорт'));
    additional.addContent(XmlService.createElement('Рахунок_контрагента').setText('(USD)'));
    xmlRoot.addContent(additional);

    // --- Секція Items ---
    const items = XmlService.createElement('Items');
    const item = XmlService.createElement('Item');

    // item.addContent(XmlService.createElement('Номер').setText('1'));
    item.addContent(XmlService.createElement('Номенклатура').setText(itemName));
    item.addContent(XmlService.createElement('Кількість').setText(quantityKg.toString()));
    item.addContent(XmlService.createElement('Од.вим.').setText('кг'));
    item.addContent(XmlService.createElement('Сума').setText(totalAmount.toString()));
    item.addContent(XmlService.createElement('Податкове_призначення_ПДВ').setText('Оподатк. ПДВ'));
    item.addContent(XmlService.createElement('Акцизні_літри').setText(exciseLitres15C.toString()));

    items.addContent(item);
    xmlRoot.addContent(items);

    // --- Форматування та збереження XML ---
    const document = XmlService.createDocument(xmlRoot);
    const xmlString = XmlService.getPrettyFormat().format(document);

    // Формуємо назву файлу як надходження товарів
    const xmlFileName = `Надходження_товарів_${timestamp}.xml`;

    // Зберігаємо файл на Google Drive з явним указанням MIME-типу
    const folder = DriveApp.getFolderById(BAS_IMPORT_XML_FOLDER_ID);
    const xmlFile = folder.createFile(xmlFileName, xmlString, 'application/xml');

    Logger.log(`[BAS Import] XML-файл успішно створено: ${xmlFile.getName()}, URL: ${xmlFile.getUrl()}`);

  } catch (error) {
    Logger.log(`[BAS Import] Помилка при генерації XML для рядка ${rowIndex}: ${error.toString()}`);
    Logger.log(`[BAS Import] Stack: ${error.stack}`);
  }
}

/**
 * Генерує XML-файл для митної декларації на основі даних з рядка аркуша "Розмитнення".
 * @param {number} rowIndex Номер рядка (починаючи з 1) в аркуші "Розмитнення".
 */
function createCustomsDeclarationXmlFile(rowIndex) {
  try {
    Logger.log(`[BAS Import] Початок генерації XML митної декларації для рядка: ${rowIndex}`);

    if (!rowIndex || typeof rowIndex !== 'number' || rowIndex < 2) {
      Logger.log('[BAS Import] Неправильний номер рядка. Має бути число більше 1.');
      return;
    }

    const spreadsheet = SpreadsheetApp.openById(BAS_IMPORT_SHEET_ID);
    const sheet = spreadsheet.getSheetByName(BAS_IMPORT_SHEET_NAME);
    if (!sheet) {
      Logger.log(`[BAS Import] Аркуш з назвою "${BAS_IMPORT_SHEET_NAME}" не знайдено.`);
      return;
    }

    const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
    const rowDataValues = sheet.getRange(rowIndex, 1, 1, sheet.getLastColumn()).getValues()[0];
    const rowData = {};
    headers.forEach((header, index) => {
      rowData[header] = rowDataValues[index];
    });

    // --- Отримання необхідних даних ---
    const itemName = rowData['Найменування вантажу'];
    const quantityKg = rowData['Маса відвантаження кг.'];
    const customsValueRaw = rowData['Митна вартість грн.'];
    const customsValue = customsValueRaw ? Number(parseFloat(customsValueRaw).toFixed(2)) : 0;
    const vmdNumber = rowData['№ВМД'];

    // Використовуємо поточну дату для митної декларації замість дати інвойсу
    const currentDate = new Date();
    const formattedDateTime = Utilities.formatDate(currentDate, Session.getScriptTimeZone(), "dd.MM.yyyy HH:mm:ss");

    // Визначення коду УКТЗЕД
    let uktzudCode = '';
    if (itemName === 'Паливо дизельне клас E' || itemName === 'Паливо дизельне клас F' || itemName === 'Паливо дизельне клас C') {
      uktzudCode = '2710194300';
    } else if (itemName === 'Бензин автомобільний А-95 "Premium"') {
      uktzudCode = '2710124512';
    }

    // --- Побудова XML ---
    const xmlRoot = XmlService.createElement('CustomsDeclaration');

    // --- Секція Основне ---
    const mainSection = XmlService.createElement('Основне');
    mainSection.addContent(XmlService.createElement('від').setText(formattedDateTime));
    mainSection.addContent(XmlService.createElement('Постачальник').setText('ОРЛЕН Летува'));
    mainSection.addContent(XmlService.createElement('Статус').setText('Випущено з митниці'));
    mainSection.addContent(XmlService.createElement('Організація').setText('МАГІСТР-Д ТОВ'));
    mainSection.addContent(XmlService.createElement('Номер_ВМД').setText(vmdNumber ? vmdNumber.toString() : ''));
    xmlRoot.addContent(mainSection);

    // --- Секція Рахунки ---
    const accountsSection = XmlService.createElement('Рахунки');
    accountsSection.addContent(XmlService.createElement('Митниця').setText('ЕНЕРГЕТИЧНА МИТНИЦЯ ДФС'));
    accountsSection.addContent(XmlService.createElement('Договір').setText('ДОГОВІР'));
    xmlRoot.addContent(accountsSection);

    // --- Секція Роздли ВМД ---
    const vmdSections = XmlService.createElement('Роздли_ВМД');

    // Перший розділ
    const vmdSection1 = XmlService.createElement('Розділ1');
    vmdSection1.addContent(XmlService.createElement('Митна_вартість').setText(customsValue.toFixed(2)));
    vmdSection1.addContent(XmlService.createElement('Код_УКТЗЕД').setText(uktzudCode));
    vmdSection1.addContent(XmlService.createElement('Ставка_ПДВ').setText('20%'));
    vmdSections.addContent(vmdSection1);

    // Другий розділ
    const vmdSection2 = XmlService.createElement('Розділ2');
    vmdSection2.addContent(XmlService.createElement('Номенклатура').setText(itemName));
    vmdSection2.addContent(XmlService.createElement('Кількість').setText(quantityKg ? quantityKg.toString() : ''));
    vmdSection2.addContent(XmlService.createElement('Од_вим').setText('кг'));
    vmdSection2.addContent(XmlService.createElement('Митна_вартість').setText(customsValue.toFixed(2)));
    vmdSection2.addContent(XmlService.createElement('Податкове_призначення').setText('Оподатк. ПДВ'));
    vmdSection2.addContent(XmlService.createElement('Стаття_декларації_ПДВ_пк').setText('11.1., 11.2. ПДВ, сплачений митним органам (до 01.2016 - 12.1. (12.1.1., 12.1.2.)'));
    vmdSection2.addContent(XmlService.createElement('Склад').setText('Товари в дорозі'));
    vmdSections.addContent(vmdSection2);

    xmlRoot.addContent(vmdSections);

    // --- Секція Додатково ---
    const additional = XmlService.createElement('Додатково');
    additional.addContent(XmlService.createElement('Менеджер').setText('Михайло'));
    additional.addContent(XmlService.createElement('Підрозділ').setText('Основна діяльність'));
    additional.addContent(XmlService.createElement('Оплата').setText('Безготівкова'));
    additional.addContent(XmlService.createElement('Порядок_розрахунків').setText('По договорах'));
    additional.addContent(XmlService.createElement('Рахунок_контрагента').setText('(UAH)'));
    xmlRoot.addContent(additional);

    // --- Форматування та збереження XML ---
    const document = XmlService.createDocument(xmlRoot);
    const xmlString = XmlService.getPrettyFormat().format(document);

    // Формуємо назву файлу
    const timestamp = Utilities.formatDate(new Date(), Session.getScriptTimeZone(), "yyyyMMddHHmmss");
    const xmlFileName = `Митна_декларація_${timestamp}.xml`;

    // Зберігаємо файл на Google Drive
    const folder = DriveApp.getFolderById(BAS_IMPORT_XML_FOLDER_ID);
    const xmlFile = folder.createFile(xmlFileName, xmlString, 'application/xml');

    Logger.log(`[BAS Import] XML-файл митної декларації успішно створено: ${xmlFile.getName()}, URL: ${xmlFile.getUrl()}`);

  } catch (error) {
    Logger.log(`[BAS Import] Помилка при генерації XML митної декларації для рядка ${rowIndex}: ${error.toString()}`);
    Logger.log(`[BAS Import] Stack: ${error.stack}`);
  }
}

function createFirstMovementXmlFile(rowIndex) {
  try {
    Logger.log(`[BAS Import] Початок генерації першого переміщення для рядка: ${rowIndex}`);

    const spreadsheet = SpreadsheetApp.openById(BAS_IMPORT_SHEET_ID);
    const sheet = spreadsheet.getSheetByName(BAS_IMPORT_SHEET_NAME);
    const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
    const rowDataValues = sheet.getRange(rowIndex, 1, 1, sheet.getLastColumn()).getValues()[0];
    const rowData = headers.reduce((acc, header, index) => {
      acc[header] = rowDataValues[index];
      return acc;
    }, {});

    // Отримання даних
    const vehicleNumbers = rowData['Номер автомобіля і причепа'].split(' ');
    const trailerNumber = vehicleNumbers.length > 1 ? vehicleNumbers[1] : '';
    const volume = parseFloat(rowData["Об'єм відвантаження л."]);
    const mass = parseFloat(rowData['Маса відвантаження кг.']);
    const density = (mass / volume).toFixed(9);
    
    // Створення XML
    const xmlRoot = XmlService.createElement('MovementDocument');
    const header = XmlService.createElement('Header');
    
    header.addContent(XmlService.createElement('від').setText(Utilities.formatDate(new Date(), Session.getScriptTimeZone(), "dd.MM.yyyy HH:mm:ss")));
    header.addContent(XmlService.createElement('Склад-відправник').setText('Товари в дорозі'));
    header.addContent(XmlService.createElement('Склад-одержувач').setText(trailerNumber));
    
    const items = XmlService.createElement('Items');
    const item = XmlService.createElement('Item');
    item.addContent(XmlService.createElement('Номенклатура').setText(rowData['Найменування вантажу']));
    item.addContent(XmlService.createElement('Кількість_літри').setText(volume.toString()));
    item.addContent(XmlService.createElement('Густина').setText(density));
    item.addContent(XmlService.createElement('Кількість').setText(mass.toString()));
    item.addContent(XmlService.createElement('Од_вим').setText('л'));
    items.addContent(item);
    
    const additional = XmlService.createElement('Additional');
    additional.addContent(XmlService.createElement('Відповідальний').setText('Михайло'));
    additional.addContent(XmlService.createElement('Підрозділ').setText('Основна діяльність'));
    additional.addContent(XmlService.createElement('Вид_ціни').setText('Прайс-лист'));

    xmlRoot.addContent(header);
    xmlRoot.addContent(items);
    xmlRoot.addContent(additional);

    // Збереження файлу
    const xmlString = XmlService.getPrettyFormat().format(XmlService.createDocument(xmlRoot));
    const fileName = `Переміщення_1_${Utilities.formatDate(new Date(), Session.getScriptTimeZone(), "yyyyMMddHHmmss")}.xml`;
    DriveApp.getFolderById(BAS_IMPORT_XML_FOLDER_ID).createFile(fileName, xmlString, 'application/xml');

  } catch (error) {
    Logger.log(`[BAS Import] Помилка генерації першого переміщення: ${error.toString()}`);
  }
}

function createSecondMovementXmlFile(rowIndex) {
  try {
    Logger.log(`[BAS Import] Початок генерації другого переміщення для рядка: ${rowIndex}`);

    const spreadsheet = SpreadsheetApp.openById(BAS_IMPORT_SHEET_ID);
    const sheet = spreadsheet.getSheetByName(BAS_IMPORT_SHEET_NAME);
    const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
    const rowDataValues = sheet.getRange(rowIndex, 1, 1, sheet.getLastColumn()).getValues()[0];
    const rowData = headers.reduce((acc, header, index) => {
      acc[header] = rowDataValues[index];
      return acc;
    }, {});

    // Отримання даних
    const vehicleNumbers = rowData['Номер автомобіля і причепа'].split(' ');
    const trailerNumber = vehicleNumbers.length > 1 ? vehicleNumbers[1] : '';
    
    // Створення XML
    const xmlRoot = XmlService.createElement('MovementDocument');
    const header = XmlService.createElement('Header');
    
    header.addContent(XmlService.createElement('від').setText(Utilities.formatDate(new Date(), Session.getScriptTimeZone(), "dd.MM.yyyy HH:mm:ss")));
    header.addContent(XmlService.createElement('Склад-відправник').setText(trailerNumber));
    header.addContent(XmlService.createElement('Склад-одержувач').setText('1004965 Оптовий нафтопродукти'));
    
    const items = XmlService.createElement('Items');
    const item = XmlService.createElement('Item');
    item.addContent(XmlService.createElement('Номенклатура').setText(rowData['Найменування вантажу']));
    item.addContent(XmlService.createElement('Кількість_літри').setText(rowData["Об'єм відвантаження л."]));
    item.addContent(XmlService.createElement('Густина').setText((rowData['Маса відвантаження кг.'] / rowData["Об'єм відвантаження л."]).toFixed(9)));
    item.addContent(XmlService.createElement('Кількість').setText(rowData['Маса відвантаження кг.']));
    item.addContent(XmlService.createElement('Од_вим').setText('л'));
    items.addContent(item);
    
    const additional = XmlService.createElement('Additional');
    additional.addContent(XmlService.createElement('Відповідальний').setText('Михайло'));
    additional.addContent(XmlService.createElement('Підрозділ').setText('Основна діяльність'));

    xmlRoot.addContent(header);
    xmlRoot.addContent(items);
    xmlRoot.addContent(additional);

    // Збереження файлу
    const xmlString = XmlService.getPrettyFormat().format(XmlService.createDocument(xmlRoot));
    const fileName = `Переміщення_2_${Utilities.formatDate(new Date(), Session.getScriptTimeZone(), "yyyyMMddHHmmss")}.xml`;
    DriveApp.getFolderById(BAS_IMPORT_XML_FOLDER_ID).createFile(fileName, xmlString, 'application/xml');

  } catch (error) {
    Logger.log(`[BAS Import] Помилка генерації другого переміщення: ${error.toString()}`);
  }
}
// Тестова функція для запуску з редактора скриптів
function test_createGoodsReceiptXmlFile() {
  createGoodsReceiptXmlFile(385); // Використовуємо рядок з реальними даними
}

function test_createCustomsDeclarationXmlFile() {
  createCustomsDeclarationXmlFile(385); // Використовуємо рядок з реальними даними
}

function test_createFirstMovementXmlFile() {
  createFirstMovementXmlFile(385); // Використовуємо рядок з реальними даними
}

function test_createSecondMovementXmlFile() {
  createSecondMovementXmlFile(385); // Використовуємо рядок з реальними даними
}
