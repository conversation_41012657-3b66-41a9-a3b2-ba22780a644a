// Функція запуску двох оновлень форм
function updateForms() {
    try {
      updateForm(); // Оновлення першої форми
      updateForm2(); // Оновлення другої форми
      Logger.log("Обидві форми успішно оновлено!");
    } catch (error) {
      Logger.log("Помилка під час оновлення форм: " + error.message);
    }
  }
  
  // Функція оновлення першої форми за назвою стовпця
  function updateForm() {
    const formId = "1QSDqxObPlc0PfIZFEjEkXZVMyez8VCAN1D8oUNFB7D4"; // ID вашої Google Форми
    const sheetId = "1IsqVkWE0jGkfzNPiPYktxzIFCnDMF0i3IbgZcglQvt8"; // ID таблиці
  
    const sheet = SpreadsheetApp.openById(sheetId).getSheetByName("Реєстр водіїв і автомобілів");
    const form = FormApp.openById(formId);
  
    // Знаходимо індекси стовпців "№ Автомобіля" і "№ причепа"
    const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
    const автоColumnIndex = headers.indexOf("№ Автомобіля") + 1;
    const причіпColumnIndex = headers.indexOf("№ причепа") + 1;
  
    if (автоColumnIndex === 0 || причіпColumnIndex === 0) {
      Logger.log("Один із стовпців ('№ Автомобіля' або '№ причепа') не знайдено у таблиці");
      return;
    }
  
    // Отримуємо дані зі стовпців
    const автоДані = sheet.getRange(2, автоColumnIndex, sheet.getLastRow() - 1).getValues().flat();
    const причіпДані = sheet.getRange(2, причіпColumnIndex, sheet.getLastRow() - 1).getValues().flat();
  
    // Об'єднуємо "Номер автомобіля" і "Причіп" через пробіл
    const номери = автоДані.map((номер, i) => {
      const причіп = причіпДані[i] || "";
      return `${номер} ${причіп}`.trim();
    }).filter(Boolean); // Прибираємо порожні значення
  
    // Знаходимо запитання "Номер автомобіля і причепа" у формі
    const formItems = form.getItems(FormApp.ItemType.LIST);
    const номерЗапитання = formItems.find(item => item.getTitle() === "Номер автомобіля і причепа");
    if (!номерЗапитання) {
      Logger.log('Запитання "Номер автомобіля і причепа" не знайдено у формі');
      return;
    }
  
    const номерЗапитанняItem = номерЗапитання.asListItem();
  
    // Вставляємо унікальні значення у форму
    const унікальніНомери = [...new Set(номери)];
    номерЗапитанняItem.setChoiceValues(унікальніНомери);
  
    Logger.log("Форма успішно оновлена!");
  }
  // Функція оновлення другої форми SHIPPING APPLICATION
  function updateForm2() {
    const formId = "1_5-1WbJsm_AQNMG6Nwnkn4yuShfQ_yXRztZw0OpHdlU"; // ID другої Google Форми
    const sheetId = "1IsqVkWE0jGkfzNPiPYktxzIFCnDMF0i3IbgZcglQvt8"; // ID таблиці
  
    const sheet = SpreadsheetApp.openById(sheetId).getSheetByName("Реєстр водіїв і автомобілів");
    const form = FormApp.openById(formId);
  
    // Знаходимо індекси всіх потрібних стовпців
    const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
    const водійColumnIndex = headers.indexOf("І.П. Англійською") + 1;
    const автоColumnIndex = headers.indexOf("№ Автомобіля") + 1;
    const причіпColumnIndex = headers.indexOf("№ причепа") + 1;
  
    if (водійColumnIndex === 0) {
      Logger.log("Стовпець 'І.П. Англійською' не знайдено у таблиці");
      return;
    }
  
    // Отримуємо дані зі стовпців
    const даніВодіїв = sheet.getRange(2, водійColumnIndex, sheet.getLastRow() - 1).getValues().flat();
    const автоДані = sheet.getRange(2, автоColumnIndex, sheet.getLastRow() - 1).getValues().flat();
    const причіпДані = sheet.getRange(2, причіпColumnIndex, sheet.getLastRow() - 1).getValues().flat();
  
    // Об'єднуємо "Номер автомобіля" і "Причіп" через пробіл
    const номери = автоДані.map((номер, i) => {
      if (!номер) return null; // Пропускаємо рядки без номера автомобіля
      const причіп = причіпДані[i] || "";
      return `${номер} ${причіп}`.trim();
    }).filter(Boolean); // Прибираємо порожні значення
  
    // Оновлюємо форму
    const formItems = form.getItems();
    
    // Оновлюємо список водіїв
    const водійЗапитання = formItems.find(item => item.getTitle() === "Водій");
    if (водійЗапитання && водійЗапитання.getType() === FormApp.ItemType.LIST) {
      const водійЗапитанняItem = водійЗапитання.asListItem();
      const унікальніВодії = [...new Set(даніВодіїв.filter(Boolean))];
      водійЗапитанняItem.setChoiceValues(унікальніВодії);
    }

    // Оновлюємо список номерів автомобілів і причепів
    const номерЗапитання = formItems.find(item => item.getTitle() === "Номер автомобіля і причепа");
    if (номерЗапитання && номерЗапитання.getType() === FormApp.ItemType.LIST) {
      const номерЗапитанняItem = номерЗапитання.asListItem();
      const унікальніНомери = [...new Set(номери)];
      номерЗапитанняItem.setChoiceValues(унікальніНомери);
    }
  
    Logger.log("Друга форма успішно оновлена!");
  }
