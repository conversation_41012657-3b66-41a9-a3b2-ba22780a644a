/**
 * Отримує поточне місцезнаходження фури з Wialon за її назвою та розраховує приблизний час прибуття (ETA) до Тернополя.
 *
 * @param {string} truckName Повна назва фури, як вона відображається в інтерфейсі Wialon Hosting.
 * @return {string} Розрахований час прибуття у форматі "Прибуття о HH:MM DD.MM.YYYY (через X год Y хв)" або повідомлення про помилку.
 * @customfunction
 */
function getTruckEta(truckName) {
    // !!! СПРОБУЄМО ВСІ ДОСТУПНІ ТОКЕНИ !!!
    const WIALON_TOKENS = [
        "3f5d86762c6d67eb1f8c351346c86aadD6C4A2F6DB23272CC145D7042F40885EC75D3EB9",
        "3f5d86762c6d67eb1f8c351346c86aad80F067365710AD934308AB2C2A405535BF4FDA27",
        "3f5d86762c6d67eb1f8c351346c86aadC3931769CC0D2458271625A706C9462D58E84E2B",
        "3f5d86762c6d67eb1f8c351346c86aadD6C4A2F6DB23272CC145D7042F40885EC75D3EB9",
        "3f5d86762c6d67eb1f8c351346c86aadE2E8F1F6DC42A5194CDB0B011325A10209561706"
    ];
    const WIALON_API_URL = "https://hst-api.wialon.com/wialon/ajax.html";
    const DESTINATION_CITY = "Тернопіль, Україна";
  
    if (!truckName) {
      return "Помилка: Не вказано назву фури.";
    }
  
    let sessionId = null;
    let unitId = null;
    let unitLat = null;
    let unitLon = null;
    let searchResult = null; // For error reporting
    let posResult = null; // For error reporting
    let successfulToken = null;

    for (const WIALON_TOKEN of WIALON_TOKENS) {
        try {
            // --- Крок 0: Вхід в систему за допомогою токена для отримання SID (eid) ---
            const loginParams = {"token": WIALON_TOKEN};
            const loginUrl = `${WIALON_API_URL}?svc=token/login&params=${encodeURIComponent(JSON.stringify(loginParams))}`;

            Logger.log(`Спроба входу з токеном: ${loginUrl.replace(WIALON_TOKEN, "СКРИТИЙ_ТОКЕН")}`); // Не логуємо сам токен
            const loginResponse = UrlFetchApp.fetch(loginUrl, {'muteHttpExceptions': true});
            const loginResult = JSON.parse(loginResponse.getContentText());

            if (loginResponse.getResponseCode() !== 200 || !loginResult || loginResult.error) {
                Logger.log(`Помилка входу з токеном (СКРИТИЙ_ТОКЕН): ${loginResponse.getResponseCode()} ${loginResponse.getContentText()}`);
                continue; // Спробувати наступний токен
            }

            if (!loginResult.eid) {
                Logger.log(`Помилка входу з токеном (СКРИТИЙ_ТОКЕН): eid не отримано. Відповідь: ${JSON.stringify(loginResult)}`);
                continue; // Спробувати наступний токен
            }

            sessionId = loginResult.eid;
            successfulToken = WIALON_TOKEN;
            Logger.log(`Успішний вхід з токеном (СКРИТИЙ_ТОКЕН). ID сесії (eid): ${sessionId}`);

            // --- Крок 1: Пошук ID об'єкта за назвою ---
            const searchParams = {
                "spec": {
                    "itemsType": "avl_unit",
                    "propName": "sys_name", // Шукаємо за системним іменем
                    "propValueMask": truckName, // Точна назва
                    "sortType": "sys_name"
                },
                "force": 1,
                "flags": 1, // Потрібні базові властивості (включаючи ID)
                "from": 0,
                "to": 0 // Шукаємо тільки один об'єкт
            };

            const searchUrl = `${WIALON_API_URL}?svc=core/search_items&params=${encodeURIComponent(JSON.stringify(searchParams))}&sid=${sessionId}`;
            const searchResponse = UrlFetchApp.fetch(searchUrl, {'muteHttpExceptions': true});
            searchResult = JSON.parse(searchResponse.getContentText()); // Зберігаємо для можливої помилки

            if (searchResponse.getResponseCode() !== 200 || !searchResult || searchResult.error) {
                Logger.log(`Помилка пошуку об'єкта з токеном (СКРИТИЙ_ТОКЕН): ${searchResponse.getResponseCode()} ${searchResponse.getContentText()}`);
                continue; // Спробувати наступний токен
            }

            if (!searchResult.items || searchResult.items.length === 0) {
                Logger.log(`Фуру з назвою "${truckName}" не знайдено з токеном (СКРИТИЙ_ТОКЕН).`);
                continue; // Спробувати наступний токен
            }

            if (searchResult.items.length > 1) {
                Logger.log(`Знайдено декілька об'єктів з назвою "${truckName}" з токеном (СКРИТИЙ_ТОКЕН): ${JSON.stringify(searchResult.items)}`);
                // Можливо, варто зупинитися тут або спробувати перший знайдений?
                // Для простоти, візьмемо перший і спробуємо отримати позицію
            }

            unitId = searchResult.items[0].id;
            Logger.log(`Знайдено ID об'єкта: ${unitId} для "${truckName}" з токеном (СКРИТИЙ_ТОКЕН)`);

            // --- Крок 2: Отримання останнього місцезнаходження за ID ---
            // Використовуємо messages/load_last для отримання останнього повідомлення з координатами
            const posParams = {
                "itemId": unitId, // messages/load_last очікує itemId
                "lastTime": 0,    // Час=0 для отримання найостаннішого повідомлення
                "lastCount": 1,   // Запитуємо тільки одне останнє повідомлення
                "flags": 1,        // Прапор 0x1 для отримання даних про позицію (pos)
                "flagsMask": 1,    // Маска для прапорів, враховуємо тільки прапор позиції
                "loadCount": 1     // Кількість повідомлень для завантаження за один запит
            };
            const posUrl = `${WIALON_API_URL}?svc=messages/load_last&params=${encodeURIComponent(JSON.stringify(posParams))}&sid=${sessionId}`;

            const posResponse = UrlFetchApp.fetch(posUrl, {'muteHttpExceptions': true});
            posResult = JSON.parse(posResponse.getContentText()); // Зберігаємо для можливої помилки

            // Відповідь messages/load_last містить масив 'messages'
            // Перевіряємо на помилку API, наявність масиву messages та координат у першому повідомленні
            if (posResponse.getResponseCode() !== 200 || !posResult || typeof posResult.error !== 'undefined' || !posResult.messages || posResult.messages.length === 0 || !posResult.messages[0].pos || typeof posResult.messages[0].pos.x === 'undefined' || typeof posResult.messages[0].pos.y === 'undefined') {
                Logger.log(`Помилка отримання позиції з токеном (СКРИТИЙ_ТОКЕН): ${posResponse.getResponseCode()} ${posResponse.getContentText()}`);
                // Якщо помилка 7 (Доступ заборонено), спробуємо інші запити для діагностики (залишаємо для логування, але не зупиняємо перебір)
                if (posResult && posResult.error === 7) {
                    Logger.log("Отримано помилку 7 (Доступ заборонено) при запиті messages/load_last з токеном (СКРИТИЙ_ТОКЕН).");
                    // Діагностичні запити залишаємо в коді, але вони будуть виконані тільки якщо цей токен буде останнім і не спрацює.
                    // Для перебору токенів, просто продовжуємо.
                }
                continue; // Спробувати наступний токен
            }

            // Якщо ми дійшли сюди, значить, координати отримано успішно з поточним токеном
            const lastMessage = posResult.messages[0];
            unitLat = lastMessage.pos.y;
            unitLon = lastMessage.pos.x;
            const lastMessageTime = lastMessage.t; // Час останнього повідомлення (Unix timestamp)
            Logger.log(`Отримано координати з токеном (СКРИТИЙ_ТОКЕН): Lat=${unitLat}, Lon=${unitLon} (Час: ${new Date(lastMessageTime * 1000).toLocaleString()})`);

            // Виходимо з циклу, оскільки координати отримано
            break;

        } catch (e) {
            Logger.log(`Виняткова ситуація при обробці токена (СКРИТИЙ_ТОКЕН): ${e.message}\nСтек: ${e.stack}`);
            continue; // Спробувати наступний токен
        }
    } // Кінець циклу перебору токенів

    // Перевіряємо, чи вдалося отримати координати хоча б з одним токеном
    if (unitLat === null || unitLon === null) {
        let errorMessage = "Не вдалося отримати координати фури з жодним з наданих токенів. Перевірте токени та їхні дозволи (потрібен 'access to messages').";
         // Якщо остання помилка була 7, додамо це до повідомлення
        if (posResult && posResult.error === 7) {
             errorMessage += " Остання спроба отримала помилку доступу (7).";
             // Виконуємо діагностичні запити тільки якщо всі токени провалились і остання помилка була 7
             Logger.log("Всі токени провалились, остання помилка була 7. Виконую діагностичні запити...");
             // Діагностика 1: Спроба отримати детальні властивості об'єкта
              try {
                  const detailParams = { "id": unitId, "flags": 0x00001001 | 0x00000008 | 0x00000200 }; // Базові + позиційні + властивості останнього повідомлення
                  const detailUrl = `${WIALON_API_URL}?svc=core/search_item&params=${encodeURIComponent(JSON.stringify(detailParams))}&sid=${sessionId}`; // Використовуємо останній отриманий SID, якщо є
                  Logger.log(`Діагностика: Запит деталей об'єкта: ${detailUrl}`);
                  const detailResponse = UrlFetchApp.fetch(detailUrl, {'muteHttpExceptions': true});
                  const detailResult = JSON.parse(detailResponse.getContentText());
                  Logger.log(`Діагностика: Відповідь деталей об'єкта (status ${detailResponse.getResponseCode()}): ${JSON.stringify(detailResult)}`);
              } catch (diagE1) {
                  Logger.log(`Діагностика: Помилка при запиті деталей об'єкта: ${diagE1.message}`);
              }

              // Діагностика 2: Спроба отримати останні значення датчиків
              try {
                  const sensorsParams = { "unitId": unitId, "calcTime": 0 }; // calcTime = 0 для останніх значень
                   const sensorsUrl = `${WIALON_API_URL}?svc=unit/calc_sensors_last&params=${encodeURIComponent(JSON.stringify(sensorsParams))}&sid=${sessionId}`; // Використовуємо останній отриманий SID, якщо є
                  Logger.log(`Діагностика: Запит останніх значень датчиків: ${sensorsUrl}`);
                  const sensorsResponse = UrlFetchApp.fetch(sensorsUrl, {'muteHttpExceptions': true});
                  const sensorsResult = JSON.parse(sensorsResponse.getContentText());
                  Logger.log(`Діагностика: Відповідь останніх значень датчиків (status ${sensorsResponse.getResponseCode()}): ${JSON.stringify(sensorsResult)}`);
              } catch (diagE2) {
                  Logger.log(`Діагностика: Помилка при запиті останніх значень датчиків: ${diagE2.message}`);
              }
        }
        return errorMessage;
    }

    // --- Крок 3: Розрахунок ETA за допомогою Google Maps ---
    // Цей блок виконується тільки якщо координати були успішно отримані
    try {
        const directions = Maps.newDirectionFinder()
            .setOrigin(unitLat, unitLon)
            .setDestination(DESTINATION_CITY)
            .setMode(Maps.DirectionFinder.Mode.DRIVING)
            .getDirections();

        if (!directions || !directions.routes || directions.routes.length === 0 || directions.routes[0].legs.length === 0) {
            Logger.log(`Не вдалося прокласти маршрут від ${unitLat},${unitLon} до ${DESTINATION_CITY}`);
            return `Помилка: Не вдалося розрахувати маршрут від поточного місцезнаходження (${unitLat}, ${unitLon}) до ${DESTINATION_CITY}.`;
        }

        const leg = directions.routes[0].legs[0];
        const durationSeconds = leg.duration.value; // Тривалість у секундах
        const durationText = leg.duration.text; // Текстове представлення тривалості (напр., "1 hour 45 mins")

        // --- Крок 4: Формування результату ---
        const now = new Date();
        // Додаємо затримку між останнім повідомленням і поточним часом, якщо вона значна
        // Час останнього повідомлення потрібно отримати з об'єкта lastMessage, який був отриманий успішним токеном
        // Якщо unitLat/unitLon не null, то lastMessage має бути доступний з успішної спроби
        const lastMessageTime = posResult.messages[0].t; // Отримуємо час з успішного результату
        const messageAgeSeconds = Math.floor(Date.now() / 1000) - lastMessageTime;
        if (messageAgeSeconds > 300) { // Якщо дані старші за 5 хвилин, додаємо час затримки
            Logger.log(`Дані позиції застарілі на ${Math.round(messageAgeSeconds / 60)} хв. Враховуємо затримку в ETA.`);
        }
        // Розрахунок часу прибуття: поточний час + тривалість шляху + вік даних
        const arrivalTime = new Date(now.getTime() + (durationSeconds * 1000) + (messageAgeSeconds * 1000));

        const options = { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric' };
        const formattedArrivalTime = arrivalTime.toLocaleString('uk-UA', options);

        return `Прибуття о ${formattedArrivalTime} (орієнтовно через ${durationText})`;

    } catch (e) {
        Logger.log(`Виняткова ситуація при розрахунку ETA: ${e.message}\nСтек: ${e.stack}`);
        return `Помилка скрипта при розрахунку ETA: ${e.message}`;
    }
}

// --- Приклад використання (можна запустити з редактора Apps Script) ---
function testEta() {
    const truckName = "Topilche Im 5 ВО3628ЕК VOLVO r"; // Замініть на реальну назву для тесту
    const eta = getTruckEta(truckName);
    Logger.log(`Результат для "${truckName}": ${eta}`);
    // Приклад виклику з Google Таблиці: =getTruckEta("Topilche Im 5 ВО3628ЕК VOLVO r")
    // або =getTruckEta(A1), де в комірці A1 є назва фури
}
