/**
 * Telegram бот для відправки сповіщень про дозвіл на розвантаження автомобілів
 * Код для Google Apps Script (прив'язаний до Google Таблиці)
 */

// Конфігурація Telegram бота
const tgBotMagistr_config = {
  // Токен бота від BotFather
  botToken: '**********:AAHYHEq7d_qKrTRupMKR4US8c3AE5plRJ-A',
  // Налаштування Google Таблиці
  sheetsConfig: {
    // ID Google Таблиці
    spreadsheetId: '1IsqVkWE0jGkfzNPiPYktxzIFCnDMF0i3IbgZcglQvt8',
    // Інформація про аркуші
    customsSheetName: 'Розмитнення',
    driversSheetName: 'Реєстр водіїв і автомобілів'
  },
  // Налаштування колонок
  columnsConfig: {
    // Колонки в аркуші Розмитнення
    customsSheet: {
      carAndTrailerCol: 'Номер автомобіля і причепа'
    },
    // Колонки в аркуші Реєстр водіїв і автомобілів
    driversSheet: {
      carNumberCol: '№ Автомобіля',
      trailerNumberCol: '№ причепа',
      chatIdCol: 'ChatID Telegam'
    }
  }
};

/**
 * Функція для відправки повідомлення в Telegram
 * @param {string} chatId - ID чату/групи куди відправити повідомлення
 * @param {string} message - Текст повідомлення
 * @returns {boolean} Успішність відправки
 */
function tgBotMagistr_sendMessage(chatId, message) {
  const MAX_RETRIES = 3; // Максимальна кількість спроб
  const RETRY_DELAY_MS = 2000; // Затримка між спробами в мілісекундах (2 секунди)
  let attempts = 0;

  while (attempts < MAX_RETRIES) {
    try {
      const tgBotMagistr_url = `https://api.telegram.org/bot${tgBotMagistr_config.botToken}/sendMessage`;
      const tgBotMagistr_payload = {
        chat_id: chatId,
        text: message,
        parse_mode: 'HTML'
      };

      const tgBotMagistr_options = {
        method: 'post',
        contentType: 'application/json',
        payload: JSON.stringify(tgBotMagistr_payload),
        muteHttpExceptions: true // Важливо для обробки помилок HTTP
      };

      const tgBotMagistr_response = UrlFetchApp.fetch(tgBotMagistr_url, tgBotMagistr_options);
      const tgBotMagistr_responseCode = tgBotMagistr_response.getResponseCode();
      const tgBotMagistr_responseDataText = tgBotMagistr_response.getContentText();
      
      // Спробуємо розпарсити відповідь, навіть якщо код не 200, бо Telegram може повертати JSON з описом помилки
      let tgBotMagistr_responseData = {};
      try {
        tgBotMagistr_responseData = JSON.parse(tgBotMagistr_responseDataText);
      } catch (e) {
        // Якщо відповідь не JSON, це може бути інша проблема
        Logger.log(`Не вдалося розпарсити відповідь від Telegram: ${tgBotMagistr_responseDataText}`);
        // Продовжуємо, щоб перевірити responseCode
      }

      if (tgBotMagistr_responseCode === 200 && tgBotMagistr_responseData.ok) {
        Logger.log('Повідомлення Telegram відправлено успішно');
        return true;
      } else {
        // Логуємо помилку з відповіді Telegram або HTTP статус
        const errorMessage = tgBotMagistr_responseData.description || `HTTP Status Code: ${tgBotMagistr_responseCode}, Response: ${tgBotMagistr_responseDataText}`;
        Logger.log(`Спроба ${attempts + 1}/${MAX_RETRIES}: Помилка при відправці повідомлення: ${errorMessage}`);
        
        // Якщо це остання спроба, не робимо затримку
        if (attempts + 1 < MAX_RETRIES) {
          Utilities.sleep(RETRY_DELAY_MS);
        }
      }
    } catch (error) {
      Logger.log(`Спроба ${attempts + 1}/${MAX_RETRIES}: Виняток при відправці повідомлення: ${error.toString()}`);
      // Якщо це остання спроба, не робимо затримку
      if (attempts + 1 < MAX_RETRIES) {
        Utilities.sleep(RETRY_DELAY_MS);
      }
    }
    attempts++;
  }

  Logger.log(`Всі ${MAX_RETRIES} спроб відправки повідомлення для chatId ${chatId} були невдалими.`);
  return false;
}

/**
 * Функція для форматування повідомлення про дозвіл на розвантаження
 * @param {string} carAndTrailerNumber - Номер автомобіля і причепа
 * @returns {string} Відформатоване повідомлення
 */
function tgBotMagistr_formatMessage(carAndTrailerNumber) {
  return `🚚 "${carAndTrailerNumber}" Дозвіл на розвантаження`;
}

/**
 * Функція для форматування повідомлення про розмитнення
 * @param {string} carAndTrailerNumber - Номер автомобіля і причепа
 * @returns {string} Відформатоване повідомлення
 */
function tgBotMagistr_formatVMDMessage(carAndTrailerNumber) {
  return `✅ "${carAndTrailerNumber}" розмитнено!`;
}

/**
 * Розбиває рядок з номерами авто і причепа на окремі значення
 * @param {string} combined - Комбінований рядок номерів (наприклад "BO9783EI BO2624XF")
 * @returns {Object} Об'єкт з окремими номерами авто і причепа
 */
function tgBotMagistr_splitCarAndTrailerNumber(combined) {
  // Розділяємо рядок за пробілом
  const tgBotMagistr_parts = combined.trim().split(' ');

  if (tgBotMagistr_parts.length >= 2) {
    return {
      carNumber: tgBotMagistr_parts[0].trim(),
      trailerNumber: tgBotMagistr_parts[1].trim()
    };
  }

  // Якщо неможливо розділити, повертаємо все як номер авто
  return {
    carNumber: combined.trim(),
    trailerNumber: ''
  };
}

/**
 * Знаходить chat_id для водія на основі номерів автомобіля і причепа
 * @param {string} combinedNumber - Комбінований номер авто і причепа (з розмитнення)
 * @returns {string|null} Chat ID групи або null якщо не знайдено
 */
function tgBotMagistr_findChatIdByCombinedNumber(combinedNumber) {
  // Розбиваємо комбінований номер на окремі частини
  const { carNumber, trailerNumber } = tgBotMagistr_splitCarAndTrailerNumber(combinedNumber);

  // Отримуємо аркуш з реєстром водіїв і автомобілів
  const tgBotMagistr_driversSheet = SpreadsheetApp.openById(tgBotMagistr_config.sheetsConfig.spreadsheetId)
    .getSheetByName(tgBotMagistr_config.sheetsConfig.driversSheetName);

  // Отримуємо всі дані з таблиці
  const tgBotMagistr_data = tgBotMagistr_driversSheet.getDataRange().getValues();
  const tgBotMagistr_headers = tgBotMagistr_data[0];

  // Знаходимо індекси потрібних колонок
  const tgBotMagistr_carNumberColIndex = tgBotMagistr_headers.indexOf(tgBotMagistr_config.columnsConfig.driversSheet.carNumberCol);
  const tgBotMagistr_trailerNumberColIndex = tgBotMagistr_headers.indexOf(tgBotMagistr_config.columnsConfig.driversSheet.trailerNumberCol);
  const tgBotMagistr_chatIdColIndex = tgBotMagistr_headers.indexOf(tgBotMagistr_config.columnsConfig.driversSheet.chatIdCol);

  // Шукаємо відповідний рядок з номерами авто і причепа
  for (let i = 1; i < tgBotMagistr_data.length; i++) {
    // Отримуємо значення з таблиці і перетворюємо в рядки
    const tgBotMagistr_currentCarNumber = String(tgBotMagistr_data[i][tgBotMagistr_carNumberColIndex] || '').trim().toUpperCase();
    const tgBotMagistr_currentTrailerNumber = String(tgBotMagistr_data[i][tgBotMagistr_trailerNumberColIndex] || '').trim().toUpperCase();

    // Перетворюємо шукані номери в верхній регістр для порівняння
    const tgBotMagistr_searchCarNumber = String(carNumber).trim().toUpperCase();
    const tgBotMagistr_searchTrailerNumber = String(trailerNumber).trim().toUpperCase();

    // Якщо знайдено обидва номери, повертаємо chat_id
    if (tgBotMagistr_currentCarNumber === tgBotMagistr_searchCarNumber &&
        tgBotMagistr_currentTrailerNumber === tgBotMagistr_searchTrailerNumber) {
      const tgBotMagistr_chatId = tgBotMagistr_data[i][tgBotMagistr_chatIdColIndex];
      if (tgBotMagistr_chatId) {
        return tgBotMagistr_chatId;
      }
    }
  }

  // Якщо не знайдено, повертаємо null
  Logger.log(`Не знайдено ChatID для авто: ${carNumber} з причепом: ${trailerNumber}`);
  return null;
}

/**
 * Головна функція для відправки сповіщень
 * Викликається тригером при заповненні форми
 */
function tgBotMagistr_sendNotification() {
  try {
    // Отримуємо об'єкт таблиці
    const tgBotMagistr_sheet = SpreadsheetApp.openById(tgBotMagistr_config.sheetsConfig.spreadsheetId)
      .getSheetByName(tgBotMagistr_config.sheetsConfig.customsSheetName);

    // Отримуємо дані з останнього рядка
    const tgBotMagistr_lastRow = tgBotMagistr_sheet.getLastRow();
    const tgBotMagistr_headers = tgBotMagistr_sheet.getRange(1, 1, 1, tgBotMagistr_sheet.getLastColumn()).getValues()[0];

    // Знаходимо індекс колонки з номером авто і причепа
    const tgBotMagistr_carAndTrailerColIndex = tgBotMagistr_headers.indexOf(tgBotMagistr_config.columnsConfig.customsSheet.carAndTrailerCol);
    // Знаходимо індекс колонки для сповіщень в Telegram
    const tgBotMagistr_notificationColIndex = tgBotMagistr_headers.indexOf('Сповіщення в Telegram');

    // Отримуємо номер авто і причепа з останнього рядка
    const tgBotMagistr_carAndTrailerNumber = tgBotMagistr_sheet.getRange(tgBotMagistr_lastRow, tgBotMagistr_carAndTrailerColIndex + 1).getValue();
    // Отримуємо статус сповіщення з останнього рядка
    const tgBotMagistr_notificationStatus = tgBotMagistr_sheet.getRange(tgBotMagistr_lastRow, tgBotMagistr_notificationColIndex + 1).getValue();

    if (!tgBotMagistr_carAndTrailerNumber) {
      Logger.log('Номер автомобіля і причепа відсутній');
      return;
    }

    Logger.log('Обробка запису для авто: ' + tgBotMagistr_carAndTrailerNumber);

    // Перевіряємо, чи потрібно відправляти сповіщення
    if (tgBotMagistr_notificationStatus !== 'Відправити сповіщення в Telegram') {
      Logger.log('Сповіщення не потрібно відправляти для авто: ' + tgBotMagistr_carAndTrailerNumber);
      return;
    }

    // Шукаємо chat_id для цих номерів
    const tgBotMagistr_chatId = tgBotMagistr_findChatIdByCombinedNumber(tgBotMagistr_carAndTrailerNumber);

    if (tgBotMagistr_chatId) {
      // Формуємо повідомлення
      const tgBotMagistr_message = tgBotMagistr_formatMessage(tgBotMagistr_carAndTrailerNumber);

      // Відправляємо повідомлення
      const tgBotMagistr_success = tgBotMagistr_sendMessage(tgBotMagistr_chatId, tgBotMagistr_message);

      // Не змінюємо статус після відправки дозволу на розвантаження, щоб зберегти можливість відправки сповіщення про розмитнення
      if (tgBotMagistr_success) {
        Logger.log('Сповіщення відправлено успішно для авто: ' + tgBotMagistr_carAndTrailerNumber);
      }
    } else {
      Logger.log('Не знайдено ChatID для авто: ' + tgBotMagistr_carAndTrailerNumber);
      // Не змінюємо статус навіть при помилці, щоб зберегти можливість відправки сповіщення про розмитнення
    }
  } catch (error) {
    Logger.log('Помилка при відправці сповіщення: ' + error.toString());
  }
}

/**
 * Функція для відправки сповіщення про розмитнення автомобіля
 * Викликається з коду ОбробкаВМД.js при отриманні файлу ВМД
 * @param {string} vehicleNumber - Номер автомобіля з файлу ВМД
 * @param {number} weight - Вага вантажу з файлу ВМД
 */
function tgBotMagistr_sendVMDNotification(vehicleNumber, weight) {
  try {
    Logger.log(`Відправка сповіщення про розмитнення для авто: ${vehicleNumber}, вага: ${weight}`);

    // Отримуємо аркуш з реєстром розмитнення
    const tgBotMagistr_sheet = SpreadsheetApp.openById(tgBotMagistr_config.sheetsConfig.spreadsheetId)
      .getSheetByName(tgBotMagistr_config.sheetsConfig.customsSheetName);

    // Отримуємо всі дані з таблиці
    const tgBotMagistr_data = tgBotMagistr_sheet.getDataRange().getValues();
    const tgBotMagistr_headers = tgBotMagistr_data[0];

    // Знаходимо індекси потрібних колонок
    const tgBotMagistr_carAndTrailerColIndex = tgBotMagistr_headers.indexOf(tgBotMagistr_config.columnsConfig.customsSheet.carAndTrailerCol);
    const tgBotMagistr_weightColIndex = tgBotMagistr_headers.indexOf('Маса відвантаження кг.');
    const tgBotMagistr_notificationColIndex = tgBotMagistr_headers.indexOf('Сповіщення в Telegram');

    // Шукаємо відповідний рядок, починаючи з кінця (нові записи)
    let tgBotMagistr_matchingRow = null;

    for (let i = tgBotMagistr_data.length - 1; i >= 1; i--) {
      const tgBotMagistr_rowVehicleNumber = tgBotMagistr_data[i][tgBotMagistr_carAndTrailerColIndex];
      const tgBotMagistr_rowWeight = parseInt(tgBotMagistr_data[i][tgBotMagistr_weightColIndex], 10);
      const tgBotMagistr_notificationStatus = tgBotMagistr_data[i][tgBotMagistr_notificationColIndex];

      // Перевіряємо, чи містить номер авто наш номер, чи співпадає вага, і чи потрібно відправляти сповіщення
      if (tgBotMagistr_rowVehicleNumber &&
          tgBotMagistr_rowVehicleNumber.includes(vehicleNumber) &&
          tgBotMagistr_rowWeight === weight &&
          tgBotMagistr_notificationStatus === 'Відправити сповіщення в Telegram') {

        tgBotMagistr_matchingRow = {
          index: i,
          vehicleNumber: tgBotMagistr_rowVehicleNumber,
          weight: tgBotMagistr_rowWeight,
          notificationColIndex: tgBotMagistr_notificationColIndex
        };
        break; // Знайшли відповідний рядок, виходимо з циклу
      }
    }

    if (!tgBotMagistr_matchingRow) {
      Logger.log(`Не знайдено відповідного запису для авто ${vehicleNumber} з вагою ${weight} або сповіщення не потрібно відправляти`);
      return;
    }

    // Шукаємо chat_id для цих номерів
    const tgBotMagistr_chatId = tgBotMagistr_findChatIdByCombinedNumber(tgBotMagistr_matchingRow.vehicleNumber);

    if (tgBotMagistr_chatId) {
      // Формуємо повідомлення
      const tgBotMagistr_message = tgBotMagistr_formatVMDMessage(tgBotMagistr_matchingRow.vehicleNumber, tgBotMagistr_matchingRow.weight);

      // Відправляємо повідомлення
      const tgBotMagistr_success = tgBotMagistr_sendMessage(tgBotMagistr_chatId, tgBotMagistr_message);

      // Якщо успішно відправлено, оновлюємо статус
      if (tgBotMagistr_success) {
        tgBotMagistr_sheet.getRange(tgBotMagistr_matchingRow.index + 1, tgBotMagistr_matchingRow.notificationColIndex + 1).setValue('Відправлено');
        Logger.log(`Сповіщення про розмитнення відправлено успішно для авто: ${tgBotMagistr_matchingRow.vehicleNumber}`);
      }
    } else {
      Logger.log(`Не знайдено ChatID для авто: ${tgBotMagistr_matchingRow.vehicleNumber}`);
      // Помічаємо, що не вдалося відправити
      tgBotMagistr_sheet.getRange(tgBotMagistr_matchingRow.index + 1, tgBotMagistr_matchingRow.notificationColIndex + 1).setValue('Помилка: не знайдено ChatID');
    }
  } catch (error) {
    Logger.log(`Помилка при відправці сповіщення про розмитнення: ${error.toString()}`);
  }
}
