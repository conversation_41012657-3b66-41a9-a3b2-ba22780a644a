const emailAddress = '<EMAIL>'; // email який буде сканований
const emailSender = '<EMAIL>'; // email відправника
const processedEmailIds = [];//масив для зберігання оброблених ID повідомлень
const newEmailIds = [];//масив для зберігання нових ID повідомлень
const folderId = '1W7o9VQ7Z2T2RTU2Xg147v8Q65ZrrMnLh'; // ID папки на Google Drive, куди будуть зберігатися PDF файли Bill_of_Lading, Invoice, Quality_Certificate
const SHEET_ID = '1IsqVkWE0jGkfzNPiPYktxzIFCnDMF0i3IbgZcglQvt8';// ID таблиці
const SHEET_NAME = 'Документи на машини';
// Додаємо константу для ключа властивості
const LAST_CHECK_KEY = 'lastCheckTime';
const PROCESSED_IDS_KEY = 'processedMessageIds';

// Функція сканує електронну пошту на нові повідомлення від конкретного користувача
function processNewEmails() {
  try {
    Logger.log('Початок пошуку нових повідомлень');

    // Отримуємо властивості скрипта
    const scriptProperties = PropertiesService.getScriptProperties();
    const lastCheckTime = scriptProperties.getProperty(LAST_CHECK_KEY);//час останньої перевірки
    const processedIds = JSON.parse(scriptProperties.getProperty(PROCESSED_IDS_KEY) || '[]');//масив оброблених ID повідомлень

    Logger.log('Останній час перевірки: ' + (lastCheckTime ? new Date(parseInt(lastCheckTime)) : 'Немає'));
    Logger.log('Кількість оброблених повідомлень: ' + processedIds.length);

    // Формуємо пошуковий запит
    let searchQuery = `from:${emailSender} has:attachment filename:pdf`;

    if (lastCheckTime) {
      const lastCheckDate = new Date(parseInt(lastCheckTime));
      const formattedDate = Utilities.formatDate(lastCheckDate, Session.getScriptTimeZone(), "yyyy/MM/dd");
      searchQuery += ` after:${formattedDate}`;
    }

    Logger.log('Пошуковий запит: ' + searchQuery);

    // Отримуємо нові повідомлення
    const threads = GmailApp.search(searchQuery);
    Logger.log(`Знайдено ${threads.length} нових ланцюжків повідомлень`);

    let newProcessedIds = [...processedIds]; // Копіюємо масив оброблених ID

    // Обробляємо кожне повідомлення
    for (let thread of threads) {
      const messages = thread.getMessages();
      for (let message of messages) {
        const messageId = message.getId();

        // Перевіряємо чи повідомлення вже було оброблено
        if (!processedIds.includes(messageId)) {
          Logger.log(`Обробка нового повідомлення ID: ${messageId}`);
          processEmailAttachments(messageId);
          newProcessedIds.push(messageId); // Додаємо ID до оброблених
        } else {
          Logger.log(`Пропуск вже обробленого повідомлення ID: ${messageId}`);
        }
      }
    }

    // Зберігаємо оновлений список оброблених повідомлень
    scriptProperties.setProperty(PROCESSED_IDS_KEY, JSON.stringify(newProcessedIds));

    // Зберігаємо поточний час як час останньої перевірки
    const currentTime = new Date().getTime();
    scriptProperties.setProperty(LAST_CHECK_KEY, currentTime.toString());

    Logger.log('Обробку CMR/Invoice завершено. Збережено новий час перевірки: ' + new Date(currentTime));

    // Примітка: Виклик функції rmpdTr_transferRmpdNumbers перенесено в головну функцію runCompleteDocumentProcess
    // Для запуску всього процесу використовуйте функцію runCompleteDocumentProcess з файлу mainRmpdProcess.js

  } catch (error) {
    Logger.log('Критична помилка в processNewEmails: ' + error.toString());
  }
}

// Функція для обробки нових повідомлень і сканування вкладень pdf
function processEmailAttachments(emailId) {
  try {
    Logger.log('Початок обробки вкладень повідомлення: ' + emailId);
    const folder = DriveApp.getFolderById(folderId);
    const message = GmailApp.getMessageById(emailId);

    const attachments = message.getAttachments();
    Logger.log(`Знайдено ${attachments.length} вкладень`);

    for (let attachment of attachments) {
      const fileName = attachment.getName().toLowerCase();

      if (fileName.includes('invoice') || fileName.includes('bill_of_lading')) {
        Logger.log('Обробка вкладення: ' + fileName);

        const blob = attachment.copyBlob();
        const pdfFile = folder.createFile(blob);
        const docText = openPdfInDocs(pdfFile);

        if (fileName.includes('bill_of_lading')) {
          addDataToSheet(docText, 'bill_of_lading');
        } else if (fileName.includes('invoice')) {
          addDataToSheet(docText, 'invoice');
        }
      }
    }

  } catch (error) {
    Logger.log('Помилка при обробці вкладень: ' + error.toString());
  }
}

// Функція яка відкриває pdf файл в Google Docs
function openPdfInDocs(pdfFile) {
  try {
    Logger.log('Початок конвертації файлу: ' + pdfFile.getName());

    let attempts = 0;
    const maxAttempts = 3;
    let docFile;

    while (attempts < maxAttempts) {
      try {
        // Додаємо паузу перед конвертацією
        Utilities.sleep(5000);

        // Конвертуємо PDF в Google Docs
        docFile = Drive.Files.copy({
          title: pdfFile.getName().replace('.pdf', '') + '_converted',
          mimeType: MimeType.GOOGLE_DOCS
        }, pdfFile.getId());

        Logger.log('Файл успішно конвертовано в Google Docs');
        break; // Виходимо з циклу, якщо успішно
      } catch (error) {
        attempts++;
        Logger.log(`Спроба ${attempts} конвертації не вдалася: ${error.toString()}`);
        if (attempts >= maxAttempts) {
          throw new Error('Не вдалося конвертувати файл після кількох спроб');
        }
      }
    }

    // Отримуємо текст з документа
    const doc = DocumentApp.openById(docFile.id);
    const text = doc.getBody().getText();

    Logger.log('Отримано текст з документа');
    return text;

  } catch (error) {
    Logger.log('Помилка при конвертації в Google Docs: ' + error.toString());
    return '';
  }
}

// Функція для обробки даних з Bill of Lading
function processBillOfLading(text) {
  try {
    Logger.log('Початок обробки тексту Bill of Lading');

    // Створюємо об'єкт для зберігання даних
    const billData = {};

    // Витягуємо номер документу (string)
    const documentNumberMatch = text.match(/Copy to sender\s*(ORC\d+)/);
    if (documentNumberMatch) {
      billData.documentNumber = documentNumberMatch[1].trim();
      Logger.log('Номер документа: ' + billData.documentNumber);
    }

    // Витягуємо дату (string)
    const dateMatch = text.match(/Data\/Date\s*(\d{4}-\d{2}-\d{2})/);
    if (dateMatch) {
      billData.date = dateMatch[1].trim();
      Logger.log('Дата: ' + billData.date);
    }

    // Витягуємо номер та дату інвойсу (string)
    const invoiceMatch = text.match(/Invoice No\.\s*([^\s]+)\s*(\d{4}-\d{2}-\d{2})/);
    if (invoiceMatch) {
      billData.invoiceNumber = invoiceMatch[1].trim();
      billData.invoiceDate = invoiceMatch[2].trim();
      Logger.log('Інвойс: ' + billData.invoiceNumber + ' від ' + billData.invoiceDate);
    }

    // Витягуємо вагу нетто (float)
    const nettoMatch = text.match(/Netto:\s*(\d+)/);
    if (nettoMatch) {
      billData.nettoWeight = parseFloat(nettoMatch[1]);
      Logger.log('Вага нетто: ' + billData.nettoWeight);
    }

    // Витягуємо об'єм (float)
    const volumeMatch = text.match(/specified\s*(\d+)/);
    if (volumeMatch) {
      billData.volume = parseFloat(volumeMatch[1]);
      Logger.log('Об\'єм: ' + billData.volume);
    }

    // Витягуємо номери пломб (string)
    // Спробуємо знайти номери пломб за різними патернами контексту

    // Масив ключових фраз, після яких може йти номер пломби
    const sealKeyPhrases = [
      'Loaded trailer sealed with seal No\\.:\\s*(?:Nuolaida\\s+)?',
      'seal No\\.:\\s*(?:Nuolaida\\s+)?',
      'sealed with seal\\s+',
      'Nuolaida\\s+'
    ];

    let sealsMatch = null;

    // Проходимо по всіх ключових фразах
    for (const phrase of sealKeyPhrases) {
      // Шукаємо будь-який текст, що йде після ключової фрази
      // Це може бути комбінація літер, цифр, пробілів і дефісів
      const regex = new RegExp(phrase + '([\\w\\s\\d-]+)', 'i');
      const match = text.match(regex);

      if (match) {
        // Знайшли потенційний номер пломби
        sealsMatch = match;
        break;
      }
    }

    // Якщо не знайдено за ключовими фразами, спробуємо знайти за позицією в документі
    // Зазвичай номери пломб знаходяться недалеко від слова "Nuolaida"
    if (!sealsMatch && text.includes('Nuolaida')) {
      // Виділяємо частину тексту навколо слова "Nuolaida"
      const nuolaidaIndex = text.indexOf('Nuolaida');
      const contextStart = Math.max(0, nuolaidaIndex - 10);
      const contextEnd = Math.min(text.length, nuolaidaIndex + 50);
      const context = text.substring(contextStart, contextEnd);

      // Шукаємо можливі номери пломб в цьому контексті
      // Це може бути комбінація літер, цифр і дефісів
      const potentialSeals = context.match(/([A-Z0-9]+[\s-]+\d+(?:-\d+)*)/);

      if (potentialSeals) {
        sealsMatch = potentialSeals;
      }
    }

    if (sealsMatch) {
      // Очищуємо знайдений текст від зайвих символів
      // Беремо тільки номери пломб, відсікаючи все зайве
      let seals = sealsMatch[1].trim();

      // Шукаємо формат "L3H 730684-692" і беремо тільки цю частину
      const exactSealMatch = seals.match(/([A-Z0-9]+\s+\d+(?:-\d+)*)/);
      if (exactSealMatch) {
        seals = exactSealMatch[0];
      }

      // Шукаємо слова після номерів пломб, які не є частиною номера
      const invalidSuffix = seals.match(/(Discount|Skirtumas|Kiekis|esant)/i);
      if (invalidSuffix) {
        // Обрізаємо все починаючи з першого недійсного слова
        seals = seals.substring(0, invalidSuffix.index).trim();
      }

      // Якщо після всіх обробок все ще є кілька слів, беремо тільки перші два
      // (зазвичай це щось на кшталт "L3H 730684-692")
      const words = seals.split(/\s+/);
      if (words.length > 2) {
        seals = words.slice(0, 2).join(' ');
      }

      billData.seals = seals;
      Logger.log('Номери пломб: ' + billData.seals);
    } else {
      Logger.log('Не вдалося знайти номери пломб у документі!');
      // Додамо виведення частини тексту для діагностики
      if (text.includes('Nuolaida')) {
        Logger.log('Знайдено слово Nuolaida. Фрагмент тексту: ' +
                  text.substring(text.indexOf('Nuolaida'),
                                 Math.min(text.indexOf('Nuolaida') + 100, text.length)));
      } else if (text.includes('seal No')) {
        Logger.log('Знайдено слово "seal No". Фрагмент тексту: ' +
                  text.substring(text.indexOf('seal No'),
                                 Math.min(text.indexOf('seal No') + 100, text.length)));
      } else {
        Logger.log('Ключові слова для пошуку пломб не знайдені');
      }
    }

    // Витягуємо номери транспорту (string)
    // Пошук за конкретним шаблоном номерів (формат AT9640IE/AT4913XF)
    let vehicleNumbersMatch = text.match(/(\w{2}\d{4}\w{2}\/\w{2}\d{4}\w{2})/);

    if (vehicleNumbersMatch) {
      Logger.log('Знайдено номери за шаблоном конкретного формату');
    }

    // Альтернативний пошук за рядком з номерами реєстрації
    if (!vehicleNumbersMatch) {
      // Шукаємо рядок, який містить номери між "Registration number" та "Brand"
      vehicleNumbersMatch = text.match(/Registration number[^\n]*\n[^\n]*\n[^\n]*?(\w+\/\w+)\s+/);

      if (vehicleNumbersMatch) {
        Logger.log('Знайдено номери за шаблоном "Registration number"');
      }
    }

    // Ще один альтернативний пошук - будь-яке поєднання літер/цифр з '/'
    if (!vehicleNumbersMatch) {
      vehicleNumbersMatch = text.match(/25[^\n]*\n[^\n]*\n[^\n]*?(\w+\/\w+)/);

      if (vehicleNumbersMatch) {
        Logger.log('Знайдено номери за шаблоном "25"');
      }
    }

    // Останній спосіб - пошук будь-якого шаблону з літер та цифр розділених "/"
    if (!vehicleNumbersMatch) {
      const allPossibleMatches = text.match(/([A-Z0-9]+\/[A-Z0-9]+)/g);
      if (allPossibleMatches && allPossibleMatches.length > 0) {
        // Беремо останній знайдений зразок (зазвичай це номери транспорту в кінці документа)
        vehicleNumbersMatch = [null, allPossibleMatches[allPossibleMatches.length - 1]];
        Logger.log('Знайдено номери за простим пошуком: ' + JSON.stringify(allPossibleMatches));
      }
    }

    if (vehicleNumbersMatch) {
      billData.vehicleNumbers = vehicleNumbersMatch[1].trim().replace('/', ' ');
      Logger.log('Номери транспорту: ' + billData.vehicleNumbers);
    } else {
      Logger.log('Не вдалося знайти номери транспорту!');
      // Додамо виведення частини тексту для діагностики
      Logger.log('Частина тексту документа: ' + text.substring(text.length - 300));
    }

    // Перевіряємо, чи всі необхідні дані були знайдені
    const requiredFields = [
      'documentNumber',
      'date',
      'invoiceNumber',
      'invoiceDate',
      'nettoWeight',
      'volume',
      'seals',
      'vehicleNumbers'
    ];

    const missingFields = requiredFields.filter(field => !billData[field]);
    if (missingFields.length > 0) {
      Logger.log('Увага! Не знайдено наступні поля: ' + missingFields.join(', '));
    }

    return billData;

  } catch (error) {
    Logger.log('Помилка при обробці Bill of Lading: ' + error.toString());
    return null;
  }
}

// Функція для обробки даних з Invoice
function processInvoice(text) {
  try {
    Logger.log('Початок обробки тексту Invoice');

    // Створюємо об'єкт для зберігання даних
    const invoiceData = {};

    // Витягуємо номер рахунку-фактури (string)
    const invoiceNumberMatch = text.match(/СЧЕТ ФАКТУРА № ([^\s]+)/);
    if (invoiceNumberMatch) {
      invoiceData.invoiceNumber = invoiceNumberMatch[1].trim();
      Logger.log('Номер рахунку-фактури: ' + invoiceData.invoiceNumber);
    }

    // Витягуємо дату виписки (string)
    const dateMatch = text.match(/Дата выписки счета:\s*(\d{4}\.\d{2}\.\d{2}\s+\d{2}:\d{2})/);
    if (dateMatch) {
      invoiceData.issueDate = dateMatch[1].trim();
      Logger.log('Дата виписки: ' + invoiceData.issueDate);
    }

    // Витягуємо номер транспортного документу (string)
    const transportDocMatch = text.match(/№ и дата транспортного документа:\s*([^,\s]+)/);
    if (transportDocMatch) {
      invoiceData.transportDocNumber = transportDocMatch[1].trim();
      Logger.log('Транспортний документ: ' + invoiceData.transportDocNumber);
    }

    // Витягуємо тип палива (string)
    const fuelTypeMatch = text.match(/(?:Dyzelinas|Дизельное топливо),\s*([A-F])\s*(?:klasė|класс)/i);
    if (fuelTypeMatch) {
      invoiceData.fuelType = 'Паливо дизельне клас ' + fuelTypeMatch[1];
      Logger.log('Тип палива: ' + invoiceData.fuelType);
    } else {
      // Шукаємо ключові слова для бензину в тексті
      Logger.log('Пошук бензину в тексті...');

      // Перевіряємо різні варіанти опису бензину
      const gasolineMatch = text.match(/(?:Benzinas|Бензин|бензин|автомобильный бензин|markės benzinas).*?(\d+).*?(?:Премиум|Premium|премиум|неэтилированный|A-\d+)/i);

      // Альтернативний пошук для автомобільного бензину
      const altGasolineMatch = text.match(/Automobilinis\s+(\d+)\s+markės\s+benzinas/i) ||
                               text.match(/неэтилированный\s+марки\s+.*?(\d+)/i) ||
                               text.match(/марки\s+.*?(\d+)/i);

      if (gasolineMatch) {
        invoiceData.fuelType = 'Бензин автомобільний А-' + gasolineMatch[1] + ' "Premium"';
        Logger.log('Тип палива (основний пошук): ' + invoiceData.fuelType);
      } else if (altGasolineMatch) {
        invoiceData.fuelType = 'Бензин автомобільний А-' + altGasolineMatch[1] + ' "Premium"';
        Logger.log('Тип палива (альтернативний пошук): ' + invoiceData.fuelType);
      } else {
        // Шукаємо будь-які згадки про бензин
        const anyGasolineRef = text.match(/бензин|Бензин|Benzinas|benzinas|БЕНЗИН|BENZINAS/i);
        if (anyGasolineRef) {
          Logger.log('Знайдено згадку про бензин, але не вдалося визначити деталі. Встановлюємо значення за замовчуванням.');
          invoiceData.fuelType = 'Бензин автомобільний А-95 "Premium"';
        } else {
          Logger.log('Не знайдено жодної згадки про бензин.');
          // Виводимо більший шматок тексту для діагностики
          Logger.log('Частина тексту документа: ' + text.substring(0, 1000) + '...');
        }
      }
    }

    // Витягуємо фактичну щільність (float)
    let densityMatch = text.match(/фактическая плотность при фактической температуре\s*(?:\(kg\/m³\))?:\s*(\d+(?:[.,]\d+)?)/);
    if (!densityMatch) {
      // Альтернативний пошук
      densityMatch = text.match(/фактическая плотность.*?:\s*(\d+(?:[.,]\d+)?)/i);
    }
    if (!densityMatch) {
      // Ще один альтернативний пошук
      densityMatch = text.match(/плотность.*?температуре.*?:\s*(\d+(?:[.,]\d+)?)/i);
    }
    if (!densityMatch) {
      // Пошук просто за словом "плотность"
      densityMatch = text.match(/плотность.*?:\s*(\d+(?:[.,]\d+)?)/i);
    }

    if (densityMatch) {
      invoiceData.actualDensity = parseFloat(densityMatch[1].replace(',', '.'));
      Logger.log('Фактична щільність: ' + invoiceData.actualDensity);
    } else {
      Logger.log('Не вдалося знайти фактичну щільність. Шукаємо в повному тексті...');
      // Шукаємо всі числа, які можуть бути щільністю
      const allDensities = text.match(/плотность.*?(\d+(?:[.,]\d+)?)/gi);
      if (allDensities && allDensities.length > 0) {
        Logger.log('Знайдені потенційні значення щільності: ' + JSON.stringify(allDensities));
      } else {
        Logger.log('Не знайдено жодного значення щільності в тексті');
      }
    }

    // Витягуємо фактичну кількість в літрах (float)
    const volumeLitersMatch = text.match(/фактическое количество \(l\):\s*(\d+(?:\s*\d+)*)/);
    if (volumeLitersMatch) {
      invoiceData.volumeLiters = parseFloat(volumeLitersMatch[1].replace(/\s+/g, ''));
      Logger.log('Фактична кількість (л): ' + invoiceData.volumeLiters);
    }

    // Витягуємо фактичну кількість в кілограмах (float)
    const volumeKgMatch = text.match(/фактическое количество \(t\):\s*(\d+[.,]\d+)/);
    if (volumeKgMatch) {
      invoiceData.volumeKg = parseFloat(volumeKgMatch[1].replace(',', '.')) * 1000; // Конвертуємо тонни в кілограми
      Logger.log('Фактична кількість (кг): ' + invoiceData.volumeKg);
    }

    // Витягуємо щільність при 15°C (float)
    const density15Match = text.match(/(?:плотность при температуре 15°C|tankis esant 15°C).*?(?:\(kg\/m³\))?:\s*(\d+(?:[.,]\d+)?)/i);
    if (density15Match) {
      invoiceData.density15C = parseFloat(density15Match[1].replace(',', '.'));
      Logger.log('Щільність при 15°C: ' + invoiceData.density15C);
    } else {
      Logger.log('Не вдалося знайти щільність при 15°C. Повний текст: ' + text);
    }

    // Витягуємо фактичну температуру (float)
    const tempMatch = text.match(/фактическая температура, °C:\s*(\d+[.,]\d+)/);
    if (tempMatch) {
      invoiceData.actualTemperature = parseFloat(tempMatch[1].replace(',', '.'));
      Logger.log('Фактична температура: ' + invoiceData.actualTemperature);
    }

    // --- Додано для витягнення Суми та Об'єму при 15°C ---
    // Витягуємо загальну суму (float)
    const totalAmountMatch = text.match(/Viso suma apmokėjimui\/Всего сумма к оплате:\s*([\d\s,.]+)/);
    if (totalAmountMatch) {
      // Видаляємо пробіли і замінюємо кому на крапку перед конвертацією
      const cleanedAmount = totalAmountMatch[1].replace(/\s/g, '').replace(',', '.');
      invoiceData.totalAmount = parseFloat(cleanedAmount);
      Logger.log('Загальна сума: ' + invoiceData.totalAmount);
    } else {
       Logger.log('Не вдалося знайти загальну суму.');
    }

    // Витягуємо об'єм в m³ при 15°C (float) і конвертуємо в літри
    const volume15Match = text.match(/m³\s+([\d.]+)/); // Шукаємо число після "m³"
    if (volume15Match) {
      const volumeM3 = parseFloat(volume15Match[1].replace(',', '.'));
      invoiceData.volume15C_Liters = Math.round(volumeM3 * 1000); // Конвертуємо в літри і заокруглюємо
      Logger.log('Об\'єм при 15°C (л): ' + invoiceData.volume15C_Liters);
    } else {
       Logger.log('Не вдалося знайти об\'єм при 15°C (m³).');
    }
    // --- Кінець доданого коду ---

    return invoiceData;

  } catch (error) {
    Logger.log('Помилка при обробці Invoice: ' + error.toString());
    return null;
  }
}

// Додаємо функцію для очищення історії оброблених повідомлень
function clearProcessingHistory() {
  try {
    const scriptProperties = PropertiesService.getScriptProperties();
    scriptProperties.deleteProperty(LAST_CHECK_KEY);
    scriptProperties.deleteProperty(PROCESSED_IDS_KEY);
    Logger.log('Історію обробки повідомлень очищено');
  } catch (error) {
    Logger.log('Помилка при очищенні історії: ' + error.toString());
  }
}

// Додаємо функцію для перегляду поточного стану
function checkProcessingState() {
  try {
    const scriptProperties = PropertiesService.getScriptProperties();
    const lastCheckTime = scriptProperties.getProperty(LAST_CHECK_KEY);
    const processedIds = JSON.parse(scriptProperties.getProperty(PROCESSED_IDS_KEY) || '[]');

    Logger.log('Останній час перевірки: ' + (lastCheckTime ? new Date(parseInt(lastCheckTime)) : 'Немає'));
    Logger.log('Кількість оброблених повідомлень: ' + processedIds.length);
    Logger.log('Оброблені ID: ' + JSON.stringify(processedIds));
  } catch (error) {
    Logger.log('Помилка при перевірці стану: ' + error.toString());
  }
}

// тестова функція для перевірки роботи функції openPdfInDocs
function testOpenPdfInDocs() {
    //   шукаємо всі pdf файли в папці
      const files = DriveApp.getFolderById(folderId).getFiles();
      // перебираємо всі pdf файли і відкриваємо їх в Google Docs
      while (files.hasNext()) {
        const file = files.next();
        openPdfInDocs(file);
      }
    }

// Додаємо функцію для парсингу дати
function parseDate(dateString) {
  if (!dateString) return '';

  let dateParts;
  let timeParts = [0, 0, 0]; // За замовчуванням час 00:00:00

  if (dateString.includes(' ')) {
    // Дата з часом, наприклад "2025.04.18 10:01"
    const [datePart, timePart] = dateString.split(' ');
    dateParts = datePart.split('.');
    timeParts = timePart.split(':').map(Number);
  } else {
    // Тільки дата, наприклад "2025-04-18"
    dateParts = dateString.split('-');
  }

  if (dateParts.length !== 3) {
    Logger.log('Неправильний формат дати: ' + dateString);
    return dateString; // Повертаємо як є, якщо формат некоректний
  }

  const year = parseInt(dateParts[0], 10);
  const month = parseInt(dateParts[1], 10) - 1; // Місяці в JS починаються з 0
  const day = parseInt(dateParts[2], 10);
  const hours = timeParts[0] || 0;
  const minutes = timeParts[1] || 0;
  const seconds = timeParts[2] || 0;

  const date = new Date(year, month, day, hours, minutes, seconds);

  if (isNaN(date.getTime())) {
    Logger.log('Неправильна дата: ' + dateString);
    return dateString;
  }

  return date;
}

// Додаємо функцію для додавання/оновлення даних в таблиці
function addDataToSheet(docText, docType) {
  try {
    Logger.log(`Початок обробки документу типу: ${docType}`);
    const sheet = SpreadsheetApp.openById(SHEET_ID).getSheetByName(SHEET_NAME);

    // Отримуємо дані з документу
    let documentData;
    let documentNumber;
    if (docType === 'invoice') {
      documentData = processInvoice(docText);
      documentNumber = documentData?.transportDocNumber;
    } else if (docType === 'bill_of_lading') {
      documentData = processBillOfLading(docText);
      documentNumber = documentData?.documentNumber;
    }

    if (!documentData || !documentNumber) {
      Logger.log('Не вдалося отримати дані з документу');
      return;
    }

    // Отримуємо всі дані з таблиці
    const data = sheet.getDataRange().getValues();
    const headers = data[0];

    // Знаходимо індекси важливих колонок
    const cmrNumberIndex = headers.indexOf('Номер CMR');
    const invoiceNumberIndex = headers.indexOf('Номер інвойсу');

    // Шукаємо відповідний рядок в таблиці
    let rowIndex = -1;
    for (let i = 1; i < data.length; i++) {
      if (docType === 'invoice' && data[i][cmrNumberIndex] === documentData.transportDocNumber) {
        rowIndex = i + 1;
        break;
      } else if (docType === 'bill_of_lading' && data[i][cmrNumberIndex] === documentData.documentNumber) {
        rowIndex = i + 1;
        break;
      }
    }

    // Підготовка даних для запису
    let rowData = new Array(headers.length).fill('');

    if (docType === 'invoice') {
      rowData = mapInvoiceDataToRow(documentData, headers);
    } else if (docType === 'bill_of_lading') {
      rowData = mapBillOfLadingDataToRow(documentData, headers);
    }

    if (rowIndex === -1) {
      // Створюємо новий рядок
      Logger.log('Створення нового рядку для документу');
      sheet.appendRow(rowData);
      rowIndex = sheet.getLastRow(); // Оновлюємо rowIndex після додавання рядка
    } else {
      // Оновлюємо існуючий рядок, зберігаючи наявні дані
      Logger.log('Оновлення існуючого рядку');
      const existingData = data[rowIndex - 1];
      const updatedData = mergeRowData(existingData, rowData, headers);
      sheet.getRange(rowIndex, 1, 1, headers.length).setValues([updatedData]);
    }

    // Налаштування формату для колонок із датами
    if (docType === 'invoice') {
      const invoiceDateIndex = headers.indexOf('Дата інвойсу');
      if (invoiceDateIndex !== -1) {
        sheet.getRange(rowIndex, invoiceDateIndex + 1).setNumberFormat("dd.mm.yyyy hh:mm:ss");
      }
    } else if (docType === 'bill_of_lading') {
      const cmrDateIndex = headers.indexOf('Дата CMR');
      if (cmrDateIndex !== -1) {
        sheet.getRange(rowIndex, cmrDateIndex + 1).setNumberFormat("dd.mm.yyyy");
      }
    }

    Logger.log('Дані успішно додано/оновлено в таблиці');

  } catch (error) {
    Logger.log('Помилка при додаванні даних в таблицю: ' + error.toString());
  }
}

// Функція для мапінгу даних з інвойсу в рядок таблиці
function mapInvoiceDataToRow(invoiceData, headers) {
  const rowData = new Array(headers.length).fill('');

  headers.forEach((header, index) => {
    switch(header) {
      case 'Номер інвойсу':
        rowData[index] = invoiceData.invoiceNumber;
        break;
      case 'Дата інвойсу':
        rowData[index] = parseDate(invoiceData.issueDate);
        break;
      case 'Номер CMR':
        rowData[index] = invoiceData.transportDocNumber;
        break;
      case 'Тип палива':
        rowData[index] = invoiceData.fuelType;
        break;
      case 'Густина (кг/м³)':
        rowData[index] = invoiceData.actualDensity;
        break;
      case 'Густина при 15°C':
        rowData[index] = invoiceData.density15C;
        break;
      case 'Температура':
        rowData[index] = invoiceData.actualTemperature;
        break;
      // --- Додано для мапінгу Суми та Об'єму при 15°C ---
      case 'Сума $':
        rowData[index] = invoiceData.totalAmount;
        break;
      case 'Об\'єм при 15°C (л)':
        rowData[index] = invoiceData.volume15C_Liters;
        break;
      // --- Кінець доданого коду ---
    }
  });

  return rowData;
}

// Функція для мапінгу даних з CMR в рядок таблиці
function mapBillOfLadingDataToRow(billData, headers) {
  const rowData = new Array(headers.length).fill('');

  headers.forEach((header, index) => {
    switch(header) {
      case 'Номер CMR':
        rowData[index] = billData.documentNumber;
        break;
      case 'Дата CMR':
        rowData[index] = parseDate(billData.date);
        break;
      case 'Номери пломб':
        rowData[index] = billData.seals;
        break;
      case 'Номер автомобіля і причепа':
        rowData[index] = billData.vehicleNumbers;
        break;
      case 'Вага (кг)':
        rowData[index] = billData.nettoWeight;
        break;
      case 'Об\'єм (л)':
        rowData[index] = billData.volume;
        break;
    }
  });

  return rowData;
}

// Функція для об'єднання даних рядків
function mergeRowData(existingData, newData, headers) {
  const mergedData = [...existingData];

  headers.forEach((header, index) => {
    if (newData[index] !== '' && newData[index] !== undefined) {
      mergedData[index] = newData[index];
    }
  });

  return mergedData;
}