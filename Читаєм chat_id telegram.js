function getGroupChatIdFromTelegram() {
    // const READ_BOT_TOKEN = '**********************************************'; // Baunti
    // const READ_BOT_TOKEN = '7461294369:AAHAsQ-2bVU8hxpeW7heWeAX7UTLa20yaHk'; // Marsik
    // const READ_BOT_TOKEN = '7733030156:AAHYHEq7d_qKrTRupMKR4US8c3AE5plRJ-A'; // Twix
    const READ_BOT_TOKEN = '7721208301:AAFkJ1lhhCP9yqpeGLhrE8QQWdJNmwsInpM'; // Chupik
    const READ_API_URL = `https://api.telegram.org/bot${READ_BOT_TOKEN}/getUpdates`;
  
    try {
        const telegramResponse = UrlFetchApp.fetch(READ_API_URL);
        const telegramData = JSON.parse(telegramResponse.getContentText());
        Logger.log('Дані від Telegram:');
        Logger.log(JSON.stringify(telegramData, null, 2));
        
        // Додаткова логіка для пошуку chat_id групи
        if (telegramData.ok && telegramData.result.length > 0) {
            for (const update of telegramData.result) {
                if (update.message && update.message.chat.type === 'group') {
                    Logger.log('Знайдено chat_id групи: ' + update.message.chat.id);
                }
            }
        }
    } catch (error) {
        Logger.log('Помилка отримання chat_id:', error.toString());
    }
}