// Константи для ID документів
const ACT_TEMPLATE_ID = '1tNVF6u0AfrxzLgnEL_fK_6r_BBqoUy3QKYyTwZNAcZQ';// ID шаблону
// перевіряємо доступ до шаблону
try {
  const templateFile = DriveApp.getFileById(ACT_TEMPLATE_ID);
  Logger.log('Шаблон доступний: ' + templateFile.getName());
}
catch (e) {
  Logger.log('Помилка доступу до шаблону: ' + e.toString());
}
const ACT_FOLDER_ID = '1RxiU4qF38j5Pt7Q3LoOHEyMNm3W6Ai9b';// ID папки для документів з підписами
const ACT_FOLDER_ID_WITHOUT_SEALS = '10iwF2q_wGL96kS-ji_xxhLk-BIlHw2z1';// ID папки для документів без підписів
const ACT_SHEET_ID = '1IsqVkWE0jGkfzNPiPYktxzIFCnDMF0i3IbgZcglQvt8';// ID таблиці
// const BOT_TOKEN_ACT = '**********:AAEtUZqdLhYlM4zHJWtXUcyJgh6M_eyr65w';// Токен Baunti
const BOT_TOKEN_ACT = '**********:AAHAsQ-2bVU8hxpeW7heWeAX7UTLa20yaHk';// Токен Marsik
const API_URL_ACT = `https://api.telegram.org/bot${BOT_TOKEN_ACT}/sendDocument`;
// const CHAT_ID_ACT = '276789696';// чат зі мною Baunti
// const CHAT_ID_ACT = '-**********'; // chat_id групи Baunti
const CHAT_ID_ACT = '276789696';// чат зі мною Marsik
// const CHAT_ID_ACT = '-**********'; // chat_id групи Marsik
const ACT_SHEET_NAME = 'Розмитнення';
const ACT_TEMPLATE_ID_WITHOUT_SEALS = '1Q0GCZ-7aIgY3u46JLlWaeTR4PQ46UBvxWbWunakDFrs'; // ID шаблону без печаток і підписів

function createResponseDocument() {
  try {
    // Відкриваємо таблицю і отримуємо дані з аркуша за його ID
    const sheet = SpreadsheetApp.openById(ACT_SHEET_ID).getSheetById(1678395711); // Вказуємо sheetId
    const lastRow = sheet.getLastRow();
    let data = sheet.getRange(lastRow, 1, 1, sheet.getLastColumn()).getValues()[0];

    // Отримуємо заголовки
    const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];

    // Створюємо об'єкт з даними
    const responseData = {};
    headers.forEach((header, index) => {
      responseData[header] = data[index];
    });

    // Розділяємо номер автомобіля і причепа
    const autoNumbers = responseData['Номер автомобіля і причепа'].split(' ');
    const vehicleNumber = autoNumbers[0];
    const trailerNumber = autoNumbers[1];

    // Перевіряємо чи потрібно автоматичне заповнення
    const autoFillIndex = headers.indexOf('Автоматичне заповнення');
    if (autoFillIndex !== -1 && data[autoFillIndex] === 'Заповнити дані про машину автоматично') {
      // Викликаємо функцію для автоматичного заповнення даних
      autoFillVehicleData(autoNumbers);

      // Оновлюємо дані після автозаповнення
      data = sheet.getRange(lastRow, 1, 1, sheet.getLastColumn()).getValues()[0];
      headers.forEach((header, index) => {
        responseData[header] = data[index];
      });
    }

    // --- Оновлена логіка для автоматичного визначення часу та резервуару ---
    // ЗАКОМЕНТОВАНО: Резервуари більше не використовуються
    // const autoTimeCheckboxIndex = headers.indexOf('Вибрати час автоматично');
    // const autoTimeCheckboxValue = autoTimeCheckboxIndex !== -1 ? responseData['Вибрати час автоматично'] : '';
    // const autoTankCheckboxIndex = headers.indexOf('Вибрати резервуар автоматично');
    // const autoTankCheckboxValue = autoTankCheckboxIndex !== -1 ? responseData['Вибрати резервуар автоматично'] : '';

    // 1. Отримуємо масив груп і часів
    // ЗАКОМЕНТОВАНО: Резервуари більше не використовуються
    // const unloadGroups = autoSelectUnloadTime(sheet, headers, responseData, lastRow, autoTimeCheckboxValue);
    // Logger.log(`Отримані групи розвантаження: ${JSON.stringify(unloadGroups)}`);
    // 2. Отримуємо список резервуарів або вручну внесений
    // const tankList = autoSelectTank(sheet, headers, responseData, lastRow, autoTankCheckboxValue);
    // Logger.log(`Отримані резервуари: ${JSON.stringify(tankList)}`);
    // 3. Вибираємо слот (резервуар і час) згідно з логікою
    // const slotResult = selectUnloadSlot(unloadGroups, tankList, responseData, sheet, headers, lastRow);
    // Logger.log(`Вибраний резервуар: ${slotResult.selectedTank}, час: ${slotResult.selectedTime}`);
    // Оновлюємо дані для подальшого використання
    // let selectedTank = slotResult.selectedTank;
    // responseData['Нафтопродукти злито в резервуар №'] = selectedTank;
    // responseData['Дата і час початку приймання вантажу'] = slotResult.selectedTime;
// --- Функція для автоматичного визначення часу розвантаження ---
// ЗАКОМЕНТОВАНО: Резервуари більше не використовуються
// Повертає масив груп із датами/часом звільнення або дату з форми
/*
function autoSelectUnloadTime(sheet, headers, responseData, lastRow, autoTimeCheckboxValue) {
  const result = [];
  const currentFuelType = responseData['Найменування вантажу'];
  if (autoTimeCheckboxValue && autoTimeCheckboxValue.toLowerCase() === 'так') {
    const unloadCompletionColumnIndex = headers.indexOf('Дата і час завершення розвантаження');
    const fuelTypeColumnIndex = headers.indexOf('Найменування вантажу');
    const tankNumberColumnIndex = headers.indexOf('Нафтопродукти злито в резервуар №');
    if (unloadCompletionColumnIndex !== -1 && fuelTypeColumnIndex !== -1 && tankNumberColumnIndex !== -1) {
      const allSheetData = sheet.getDataRange().getValues();
      const now = new Date();
      if (currentFuelType && currentFuelType.toLowerCase().includes('диз')) {
        const dieselGroups = [
          { name: '1-8', min: 1, max: 8 },
          { name: '9-16', min: 9, max: 16 },
          { name: '29-33', min: 29, max: 33 }
        ];
        dieselGroups.forEach(group => {
          let groupBusyUntil = null;
          let tanks = [];
          for (let i = 1; i < allSheetData.length; i++) {
            const completionTime = allSheetData[i][unloadCompletionColumnIndex];
            const fuelType = allSheetData[i][fuelTypeColumnIndex];
            const tankNumber = allSheetData[i][tankNumberColumnIndex];
            const tankNum = parseInt(tankNumber, 10);
            if (fuelType && fuelType.toLowerCase().includes('диз') && tankNum >= group.min && tankNum <= group.max) {
              if (completionTime && new Date(completionTime) > now) {
                const completionDate = new Date(completionTime);
                if (!groupBusyUntil || completionDate > groupBusyUntil) groupBusyUntil = completionDate;
              }
            }
          }
          // Тут tanks можна наповнити з getFreeTankByFuelType, але для простоти — залишаємо порожній, бо вибір резервуару окремо
          result.push({ group: group.name, availableTime: groupBusyUntil || now, tanks });
        });
      } else if (currentFuelType && currentFuelType.toLowerCase().includes('бенз')) {
        // Для бензину одна група
        let groupBusyUntil = null;
        for (let i = 1; i < allSheetData.length; i++) {
          const completionTime = allSheetData[i][unloadCompletionColumnIndex];
          const fuelType = allSheetData[i][fuelTypeColumnIndex];
          const tankNumber = allSheetData[i][tankNumberColumnIndex];
          const tankNum = parseInt(tankNumber, 10);
          if (fuelType && fuelType.toLowerCase().includes('бенз') && tankNum >= 17 && tankNum <= 24) {
            if (completionTime && new Date(completionTime) > now) {
              const completionDate = new Date(completionTime);
              if (!groupBusyUntil || completionDate > groupBusyUntil) groupBusyUntil = completionDate;
            }
          }
        }
        result.push({ group: '17-24', availableTime: groupBusyUntil || new Date(), tanks: [] });
      }
      return result;
    }
  }
  // Якщо не вибрано автоматично — повертаємо дату з форми
  return [{ group: null, availableTime: responseData['Дата і час початку приймання вантажу'] || new Date(), tanks: [] }];
}
*/

    // Форматуємо дату приймання
    const date = responseData['Дата і час початку приймання вантажу'];
    const datePruimanna = formatDateTime_ddMMyyyy(date);

    const temperature = getCurrentTemperature('Ternopil');
    const densityAtUnload = calculateDensityAtUnload(
      responseData['Температура відвантаження С°'],
      temperature,
      responseData['Густина відвантаження кг/м³'],
      responseData['Густина відвантаження при температурі 15 С° кг/м³']
    );

    // форматуємо дату і час початку розвантаження
    const startTime =  responseData['Дата і час початку приймання вантажу'];
    const formattedStartTime = formatDateTime(startTime);
    // формуємо дату і час розвантаження
    const getstartTime = responseData['Дата і час початку приймання вантажу'];
    const endtime = calculateEndTime(getstartTime);
    const formattedEndTime = formatDateTime(endtime);
    // ЗАКОМЕНТОВАНО: Резервуари більше не використовуються
    // selectedTank = autoSelectTank(sheet, headers, responseData, lastRow);
    let selectedTank = ''; // Порожнє значення замість автоматичного вибору резервуару
// --- Функція для автоматичного вибору резервуару ---
// ЗАКОМЕНТОВАНО: Резервуари більше не використовуються
// Повертає список вільних резервуарів або вручну внесений резервуар
/*
function autoSelectTank(sheet, headers, responseData, lastRow, autoTankCheckboxValue) {
  if (autoTankCheckboxValue && autoTankCheckboxValue.toLowerCase() === 'так') {
    // Повертаємо список вільних резервуарів для типу пального
    const fuelType = responseData['Найменування вантажу'];
    const tanks = getFreeTankByFuelType(fuelType);
    if (Array.isArray(tanks)) return tanks;
    if (typeof tanks === 'object' && tanks.number) return [tanks.number];
    if (typeof tanks === 'string') return [tanks];
    return [];
  } else {
    // Повертаємо вручну внесений резервуар
    return [responseData['Нафтопродукти злито в резервуар №']];
  }
}
*/
// ЗАКОМЕНТОВАНО: Резервуари більше не використовуються
// Приймає масив груп і список резервуарів, повертає {selectedTank, selectedTime}, оновлює таблицю
/*
function selectUnloadSlot(unloadGroups, tankList, responseData, sheet, headers, lastRow) {
  // Якщо резервуар і час обираються автоматично
  let selectedTank = null;
  let selectedTime = null;
  // Приклад логіки: вибрати резервуар із групи, яка звільниться найшвидше, і встановити відповідний час
  // Якщо tankList — масив вільних резервуарів, unloadGroups — масив груп із часом
  // Якщо tankList — вручну, unloadGroups — вручну
  if (tankList.length === 1 && unloadGroups.length === 1 && !unloadGroups[0].group) {
    // Все вручну
    selectedTank = tankList[0];
    selectedTime = unloadGroups[0].availableTime;
    // Якщо резервуар вручну, але він є у списку вільних — видалити
    if (selectedTank && responseData['Найменування вантажу']) {
      removeTank(selectedTank, responseData['Найменування вантажу']);
    }
    // Якщо час автоматичний (тобто чекбокс був "так"), округлити до кратності 5 хвилин
    if (responseData['Вибрати час автоматично'] && responseData['Вибрати час автоматично'].toLowerCase() === 'так' && selectedTime) {
      selectedTime = roundUpToNearest5MinutesAndAdd5(new Date(selectedTime));
    }
  } else {
    // Автоматичний вибір: знаходимо групу з найближчим availableTime, де є резервуари
    let bestGroup = null;
    for (let group of unloadGroups) {
      if (!bestGroup || group.availableTime < bestGroup.availableTime) {
        bestGroup = group;
      }
    }
    // Вибираємо резервуар із tankList, який належить до bestGroup (якщо можливо)
    let tankInGroup = null;
    if (bestGroup && bestGroup.group) {
      tankInGroup = tankList.find(tank => {
        const num = parseInt(tank, 10);
        if (bestGroup.group === '1-8') return num >= 1 && num <= 8;
        if (bestGroup.group === '9-16') return num >= 9 && num <= 16;
        if (bestGroup.group === '29-33') return num >= 29 && num <= 33;
        if (bestGroup.group === '17-24') return num >= 17 && num <= 24;
        return false;
      });
    }
    selectedTank = tankInGroup || tankList[0];
    selectedTime = bestGroup ? bestGroup.availableTime : new Date();
    // Округлення часу до кратності 5 хвилин
    if (selectedTime) {
      selectedTime = roundUpToNearest5MinutesAndAdd5(new Date(selectedTime));
    }
    // Видаляємо резервуар зі списку вільних
    if (selectedTank && responseData['Найменування вантажу']) {
      removeTank(selectedTank, responseData['Найменування вантажу']);
    }
  }
  // Оновлюємо таблицю
  if (selectedTank) {
    sheet.getRange(lastRow, headers.indexOf('Нафтопродукти злито в резервуар №') + 1).setValue(selectedTank);
  }
  if (selectedTime) {
    sheet.getRange(lastRow, headers.indexOf('Дата і час початку приймання вантажу') + 1).setValue(selectedTime);
  }
  return { selectedTank, selectedTime };
}
*/

    // Створюємо об'єкт для заміни плейсхолдерів
    // Отримуємо останній номер акту з пам'яті (PropertiesService)
    const scriptProperties = PropertiesService.getScriptProperties();
    let lastActNumber = parseInt(scriptProperties.getProperty('lastActNumber') || '518', 10);// початкове значення 517

    // Збільшуємо номер для нового акту
    lastActNumber += 1;
    // Зберігаємо новий номер у пам'яті
    scriptProperties.setProperty('lastActNumber', lastActNumber);

    const replacements = {
      '{{Номер по порядку}}': lastActNumber,
      '{{Дата приймання}}': datePruimanna,
      '{{Найменування вантажу}}': responseData['Найменування вантажу'],
      // '{{Місце приймання}}': responseData['Місце  приймання нафтопродутів'],
      '{{Дата і час прибуття}}': formatDateTime(responseData['Дата прибуття вантажу']),
      '{{Перша пломба}}': responseData['Номер першої пломби'],
      '{{Остання пломба}}': responseData['Номер останньої пломби'],
      '{{Час початку}}': formattedStartTime,
      '{{Час закінчення}}': formattedEndTime,
      '{{Обєм З}}': responseData['Об\'єм відвантаження л.'],
      '{{Обєм Р}}': calculateVolume(responseData['Маса відвантаження кг.'], densityAtUnload),
      '{{Густина З}}': responseData['Густина відвантаження кг/м³'],
      '{{Густина Р}}': densityAtUnload,
      '{{З С°}}': responseData['Температура відвантаження С°'],
      '{{Р С°}}': temperature,
      '{{Маса, кг}}': responseData['Маса відвантаження кг.'],
      // ЗАКОМЕНТОВАНО: Резервуари більше не використовуються
      // '{{Резервуар №}}': selectedTank,
      '{{Номер автомобіля}}': vehicleNumber,
      '{{Номер причепа}}': trailerNumber
    };

    // ЗАКОМЕНТОВАНО: Резервуари більше не використовуються
    // Якщо резервуар вибрано автоматично, також записуємо його у таблицю "Розмитнення"
    /*
    if (
      responseData['Вибрати резервуар автоматично'] &&
      responseData['Вибрати резервуар автоматично'].toLowerCase() === 'так' &&
      selectedTank
    ) {
      const customsSheet = SpreadsheetApp.openById(ACT_SHEET_ID).getSheetByName('Розмитнення');
      const customsHeaders = customsSheet.getRange(1, 1, 1, customsSheet.getLastColumn()).getValues()[0];
      const tankColumnIdx = customsHeaders.indexOf('Нафтопродукти злито в резервуар №');
      if (tankColumnIdx !== -1) {
        customsSheet.getRange(lastRow, tankColumnIdx + 1).setValue(selectedTank);
      }
    }
    */

    // Створюємо копію шаблону 1 (з печатками і підписами)
    const templateFile = DriveApp.getFileById(ACT_TEMPLATE_ID);
    const folder = DriveApp.getFolderById(ACT_FOLDER_ID);

    const newFileName = `Акт №${lastActNumber} від ${datePruimanna} ${vehicleNumber} ${trailerNumber}`;
    const newFile = templateFile.makeCopy(newFileName, folder);
    const doc = DocumentApp.openById(newFile.getId());

    // Отримуємо тіло документу 1
    const body = doc.getBody();

    // Заміняємо плейсхолдери значеннями для шаблону 1
    Object.keys(replacements).forEach(placeholder => {
      if (replacements[placeholder]) {
        body.replaceText(placeholder, replacements[placeholder].toString());
      }
    });

    // Зберігаємо зміни шаблону 1
    doc.saveAndClose();

    // Отримуємо папку для збереження документів без печаток і підписів
    const folderWithoutSeals = DriveApp.getFolderById(ACT_FOLDER_ID_WITHOUT_SEALS);

    // Створюємо копію шаблону 2 (без печаток і підписів)
    const templateFile2 = DriveApp.getFileById(ACT_TEMPLATE_ID_WITHOUT_SEALS);
    const newFileName2 = `Акт №${lastActNumber} від ${datePruimanna} ${vehicleNumber} ${trailerNumber} без печаток і підписів`;
    const newFile2 = templateFile2.makeCopy(newFileName2, folderWithoutSeals);
    const doc2 = DocumentApp.openById(newFile2.getId());

    // Отримуємо тіло документу 2
    const body2 = doc2.getBody();

    // Заміняємо плейсхолдери значеннями для шаблону 2
    Object.keys(replacements).forEach(placeholder => {
      if (replacements[placeholder]) {
        body2.replaceText(placeholder, replacements[placeholder].toString());
      }
    });

    // Зберігаємо зміни шаблону 2
    doc2.saveAndClose();

    // Генеруємо PDF
    const pdfBlob = DriveApp.getFileById(newFile.getId()).getAs('application/pdf');
    const pdfFile = folder.createFile(pdfBlob).setName(newFileName + '.pdf');

    // Записуємо посилання на Google Документ та PDF назад в таблицю
    const docUrlColumn = headers.indexOf('Посилання на акт') + 1;
    const pdfUrlColumn = headers.indexOf('Посилання на PDF') + 1; // Додати колонку для посилання на PDF
    const docWithoutSignsColumn = headers.indexOf('Акт без підпису') + 1; // Додаємо колонку для акту без підписів

    if (docUrlColumn > 0) {
      sheet.getRange(lastRow, docUrlColumn).setValue(newFile.getUrl());
    }
    if (pdfUrlColumn > 0) {
      sheet.getRange(lastRow, pdfUrlColumn).setValue(pdfFile.getUrl());
    }
    if (docWithoutSignsColumn > 0) {
      sheet.getRange(lastRow, docWithoutSignsColumn).setValue(newFile2.getUrl());
      Logger.log(`Записано посилання на акт без підпису: ${newFile2.getUrl()}`);
    }

    // Записуємо дату і час завершення розвантаження та посилання на PDF в таблицю
    const unloadTimeColumn = headers.indexOf('Дата і час завершення розвантаження') + 1;
    const pdfLinkColumn = headers.indexOf('Посилання на акт') + 1;
    const statusColumn = headers.indexOf('Статус відправки') + 1;
    if (unloadTimeColumn > 0) {
      sheet.getRange(lastRow, unloadTimeColumn).setValue(endtime);
    }
    if (pdfLinkColumn > 0) {
      sheet.getRange(lastRow, pdfLinkColumn).setValue(pdfFile.getUrl());
    }

    // Перевіряємо, чи настав час відправки
    const now = new Date();
    if (endtime <= now) {
      sendActDocumentToTelegram(pdfFile.getId(), CHAT_ID_ACT);
      if (statusColumn > 0) {
        sheet.getRange(lastRow, statusColumn).setValue('Відправлено');
      }
    } else {
      if (statusColumn > 0) {
        sheet.getRange(lastRow, statusColumn).setValue('Очікує відправки');
      }
    }

    Logger.log(`Документ успішно створено: ${newFileName}`);
  } catch (error) {
    Logger.log(`Помилка: ${error.toString()}`);
    throw error;
  }
}

// ЗАКОМЕНТОВАНО: Резервуари більше не використовуються
// Функція для отримання вільного резервуару за типом пального
/*
function getFreeTankByFuelType(fuelType) {
  const FREE_TANKS_SHEET_NAME = 'Резервуари';
  const sheet = SpreadsheetApp.openById(ACT_SHEET_ID).getSheetByName(FREE_TANKS_SHEET_NAME);
  if (!sheet) {
    Logger.log('Не знайдено аркуша "Резервуари"');
    return '';
  }
  const lastRow = sheet.getLastRow();
  if (lastRow < 2) {
    Logger.log('Немає даних в аркуші "Резервуари"');
    return '';
  }
  const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
  const data = sheet.getRange(lastRow, 1, 1, sheet.getLastColumn()).getValues()[0];
  const dieselIdx = headers.indexOf('Вільні резервуари дизель');
  const petrolIdx = headers.indexOf('Вільні резервуари бензин');
  let dieselTanks = dieselIdx !== -1 ? String(data[dieselIdx] || '').split(',').map(s => s.trim()).filter(Boolean) : [];
  let petrolTanks = petrolIdx !== -1 ? String(data[petrolIdx] || '').split(',').map(s => s.trim()).filter(Boolean) : [];
  Logger.log(`Доступні резервуари дизеля: ${dieselTanks.join(', ')}`);
  Logger.log(`Доступні резервуари бензину: ${petrolTanks.join(', ')}`);
  let selectedTank = '';

  if (fuelType && fuelType.toLowerCase().includes('диз')) {
    // Визначаємо групи резервуарів для дизеля
    const dieselGroups = [
      { name: '1-8', min: 1, max: 8, tanks: [] },
      { name: '9-16', min: 9, max: 16, tanks: [] },
      { name: '29-33', min: 29, max: 33, tanks: [] }
    ];

    // Розподіляємо доступні резервуари по групах
    dieselTanks.forEach(tank => {
      const num = parseInt(tank, 10);
      for (let group of dieselGroups) {
        if (num >= group.min && num <= group.max) {
          group.tanks.push(tank);
          break;
        }
      }
    });

    // Перевіряємо активні розвантаження в аркуші "Розмитнення"
    const customsSheet = SpreadsheetApp.openById(ACT_SHEET_ID).getSheetByName('Розмитнення');
    const customsHeaders = customsSheet.getRange(1, 1, 1, customsSheet.getLastColumn()).getValues()[0];
    const allCustomsData = customsSheet.getDataRange().getValues();

    const fuelTypeColumnIndex = customsHeaders.indexOf('Найменування вантажу');
    const tankNumberColumnIndex = customsHeaders.indexOf('Нафтопродукти злито в резервуар №');
    const unloadCompletionColumnIndex = customsHeaders.indexOf('Дата і час завершення розвантаження');


    // Зберігаємо час завершення для активних розвантажень по групах
    const activeUnloadings = {
      '1-8': null,
      '9-16': null,
      '29-33': null
    };

    // Масив для зберігання всіх активних груп дизельних розвантажень
    const activeDieselGroups = [];

    const now = new Date();

    // Перевіряємо активні розвантаження
    if (fuelTypeColumnIndex !== -1 && tankNumberColumnIndex !== -1 && unloadCompletionColumnIndex !== -1) {
      for (let i = 1; i < allCustomsData.length; i++) {
        const completionTime = allCustomsData[i][unloadCompletionColumnIndex];
        const fuelType = allCustomsData[i][fuelTypeColumnIndex];
        const tankNumber = allCustomsData[i][tankNumberColumnIndex];

        if (completionTime && new Date(completionTime) > now &&
            fuelType && fuelType.toLowerCase().includes('диз') &&
            tankNumber) {

          const completionDate = new Date(completionTime);
          const tankNum = parseInt(tankNumber, 10);

          // Визначаємо до якої групи належить резервуар
          let groupName = null;
          if (tankNum >= 1 && tankNum <= 8) groupName = '1-8';
          else if (tankNum >= 9 && tankNum <= 16) groupName = '9-16';
          else if (tankNum >= 29 && tankNum <= 33) groupName = '29-33';

          if (groupName) {
            // Оновлюємо час завершення для групи, якщо він пізніший
            if (!activeUnloadings[groupName] || completionDate > activeUnloadings[groupName]) {
              activeUnloadings[groupName] = completionDate;
            }

            // Додаємо групу до списку активних, якщо її ще немає
            if (!activeDieselGroups.includes(groupName)) {
              activeDieselGroups.push(groupName);
            }
          }
        }
      }
    }

    Logger.log(`Активні розвантаження по групах: ${JSON.stringify(activeUnloadings)}`);
    Logger.log(`Активні групи дизельних розвантажень: ${activeDieselGroups.join(', ')}`);
    Logger.log(`Кількість активних груп дизельних розвантажень: ${activeDieselGroups.length}`);

    // Визначаємо, які групи зайняті
    dieselGroups.forEach(group => {
      group.isBusy = activeUnloadings[group.name] !== null;
      group.busyUntil = activeUnloadings[group.name];
    });

    // Перевіряємо обмеження на кількість одночасних розвантажень дизеля (максимум 2 насоси)
    const maxDieselPumps = 2;
    const isMaxDieselPumpsReached = activeDieselGroups.length >= maxDieselPumps;

    if (isMaxDieselPumpsReached) {
      Logger.log(`Досягнуто максимальну кількість одночасних розвантажень дизеля (${maxDieselPumps}). Потрібно чекати звільнення насосу.`);

      // Сортуємо активні групи за часом звільнення (від раннього до пізнього)
      const sortedActiveGroups = [...activeDieselGroups].sort((a, b) => {
        if (!activeUnloadings[a]) return 1;
        if (!activeUnloadings[b]) return -1;
        return activeUnloadings[a] - activeUnloadings[b];
      });

      // Вибираємо групу, яка звільниться найраніше
      const earliestFreeGroup = sortedActiveGroups[0];
      Logger.log(`Група ${earliestFreeGroup} звільниться найраніше: ${activeUnloadings[earliestFreeGroup]}`);

      // Шукаємо групу з доступними резервуарами, яка звільниться найраніше
      const groupsWithTanks = dieselGroups.filter(group => group.tanks.length > 0);

      // Сортуємо групи з резервуарами за часом звільнення
      groupsWithTanks.sort((a, b) => {
        if (!a.busyUntil) return -1; // Вільна група має пріоритет
        if (!b.busyUntil) return 1;
        return a.busyUntil - b.busyUntil;
      });

      if (groupsWithTanks.length > 0) {
        // Вибираємо резервуар з групи, яка звільниться найраніше
        const selectedGroup = groupsWithTanks[0];
        const sortedTanks = selectedGroup.tanks.sort((a, b) => parseInt(b, 10) - parseInt(a, 10));
        selectedTank = sortedTanks[0];

        Logger.log(`Всі насоси для дизеля зайняті. Вибрано резервуар ${selectedTank} з групи ${selectedGroup.name}, яка звільниться ${selectedGroup.busyUntil || 'зараз'}`);

        // Зберігаємо інформацію про час звільнення групи для використання при встановленні часу початку розвантаження
        selectedTank = {
          number: selectedTank,
          group: selectedGroup.name,
          availableTime: selectedGroup.busyUntil || now
        };
      } else {
        Logger.log(`Не знайдено груп з доступними резервуарами.`);
      }
    } else {
      // Якщо не досягнуто максимальної кількості насосів, використовуємо стандартну логіку

      // Спочатку шукаємо вільні групи з доступними резервуарами
      const freeGroups = dieselGroups.filter(group => !group.isBusy && group.tanks.length > 0);

      if (freeGroups.length > 0) {
        // Сортуємо вільні групи за кількістю доступних резервуарів (від більшого до меншого)
        freeGroups.sort((a, b) => b.tanks.length - a.tanks.length);
        // Вибираємо резервуар з групи, яка має найбільше доступних резервуарів
        // Сортуємо резервуари за номером (від більшого до меншого)
        const sortedTanks = freeGroups[0].tanks.sort((a, b) => parseInt(b, 10) - parseInt(a, 10));
        const tankNumber = sortedTanks[0];

        Logger.log(`Вибрано резервуар ${tankNumber} з вільної групи ${freeGroups[0].name}`);

        // Зберігаємо інформацію про вибраний резервуар та його групу
        selectedTank = {
          number: tankNumber,
          group: freeGroups[0].name,
          availableTime: now
        };
      } else {
        // Якщо немає вільних груп, шукаємо групу, яка звільниться найраніше
        const busyGroups = dieselGroups.filter(group => group.tanks.length > 0);

        if (busyGroups.length > 0) {
          // Сортуємо зайняті групи за часом звільнення (від раннього до пізнього)
          busyGroups.sort((a, b) => {
            if (!a.busyUntil) return 1;
            if (!b.busyUntil) return -1;
            return a.busyUntil - b.busyUntil;
          });

          // Вибираємо резервуар з групи, яка звільниться найраніше
          // Сортуємо резервуари за номером (від більшого до меншого)
          const sortedTanks = busyGroups[0].tanks.sort((a, b) => parseInt(b, 10) - parseInt(a, 10));
          const tankNumber = sortedTanks[0];

          Logger.log(`Вибрано резервуар ${tankNumber} з зайнятої групи ${busyGroups[0].name}, яка звільниться ${busyGroups[0].busyUntil}`);

          // Зберігаємо інформацію про вибраний резервуар та його групу
          selectedTank = {
            number: tankNumber,
            group: busyGroups[0].name,
            availableTime: busyGroups[0].busyUntil || now
          };
        }
      }
    }
  } else if (fuelType && fuelType.toLowerCase().includes('бенз')) {
    // Логіка для бензину залишається незмінною
    if (petrolTanks.length > 0) {
      // Для бензину повертаємо просто номер резервуару, оскільки є тільки одна група
      selectedTank = petrolTanks[0];
    }
  }

  return selectedTank;
}
*/

// ЗАКОМЕНТОВАНО: Резервуари більше не використовуються
// Функція для видалення резервуару зі списку вільних за типом пального
/*
function removeTank(tankNumber, fuelType) {
  // Якщо tankNumber є об'єктом (для дизеля), отримуємо номер резервуару
  if (typeof tankNumber === 'object' && tankNumber.number) {
    tankNumber = tankNumber.number;
  }

  const FREE_TANKS_SHEET_NAME = 'Резервуари';
  const sheet = SpreadsheetApp.openById(ACT_SHEET_ID).getSheetByName(FREE_TANKS_SHEET_NAME);
  if (!sheet) {
    Logger.log('Не знайдено аркуша "Резервуари"');
    return;
  }
  const lastRow = sheet.getLastRow();
  if (lastRow < 2) {
    Logger.log('Немає даних в аркуші "Резервуари"');
    return;
  }
  const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
  const data = sheet.getRange(lastRow, 1, 1, sheet.getLastColumn()).getValues()[0];
  const dieselIdx = headers.indexOf('Вільні резервуари дизель');
  const petrolIdx = headers.indexOf('Вільні резервуари бензин');

  if (fuelType && fuelType.toLowerCase().includes('диз') && dieselIdx !== -1) {
    let dieselTanks = String(data[dieselIdx] || '').split(',').map(s => s.trim()).filter(Boolean);
    const initialLength = dieselTanks.length;
    dieselTanks = dieselTanks.filter(t => t !== tankNumber);
    if (dieselTanks.length < initialLength) {
      sheet.getRange(lastRow, dieselIdx + 1).setValue(dieselTanks.join(', '));
      Logger.log(`Резервуар ${tankNumber} видалено зі списку дизельних резервуарів.`);
    } else {
      Logger.log(`Резервуар ${tankNumber} не знайдено у списку дизельних резервуарів.`);
    }
  } else if (fuelType && fuelType.toLowerCase().includes('бенз') && petrolIdx !== -1) {
    let petrolTanks = String(data[petrolIdx] || '').split(',').map(s => s.trim()).filter(Boolean);
    const initialLength = petrolTanks.length;
    petrolTanks = petrolTanks.filter(t => t !== tankNumber);
    if (petrolTanks.length < initialLength) {
      sheet.getRange(lastRow, petrolIdx + 1).setValue(petrolTanks.join(', '));
      Logger.log(`Резервуар ${tankNumber} видалено зі списку бензинових резервуарів.`);
    } else {
      Logger.log(`Резервуар ${tankNumber} не знайдено у списку бензинових резервуарів.`);
    }
  } else {
    Logger.log(`Невідомий тип пального або не знайдено відповідну колонку для резервуарів.`);
  }
}
*/

// Функція для відправки документу в Telegram
function sendActDocumentToTelegram(documentId, chatId) {
  if (!documentId || !chatId) {
    Logger.log('Missing parameters for sending document');
    return false;
  }

  Logger.log(`Відправка документу: ${documentId} до чату: ${chatId}`);

  const documentBlob = DriveApp.getFileById(documentId).getBlob();
  const payload = {
    chat_id: chatId,
    document: documentBlob
  };

  const options = {
    method: 'post',
    payload: payload,
    muteHttpExceptions: true
  };

  let success = false;
  let lastError = '';
  for (let attempt = 1; attempt <= 3; attempt++) {
    try {
      const response = UrlFetchApp.fetch(API_URL_ACT, options);
      const code = response.getResponseCode();
      if (code >= 200 && code < 300) {
        Logger.log('документ відправлено успішно (спроба ' + attempt + ')');
        success = true;
        break;
      } else {
        lastError = 'HTTP ' + code + ': ' + response.getContentText();
        Logger.log('Спроба ' + attempt + ' не вдалася: ' + lastError);
      }
    } catch (error) {
      lastError = error.message;
      Logger.log('Спроба ' + attempt + ' не вдалася: ' + lastError);
    }
    if (attempt < 3) {
      Logger.log('Очікування 10 секунд перед наступною спробою...');
      Utilities.sleep(10000);
    }
  }
  if (!success) {
    Logger.log('Error sending document after 3 attempts: ' + lastError);
  }
  return success;
}

// Функція для перевірки і відправки документів в Telegram
function checkAndSendDocuments() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(ACT_SHEET_NAME);
  const data = sheet.getDataRange().getValues();
  const headers = data[0];
  const unloadTimeColumn = headers.indexOf('Дата і час завершення розвантаження');
  const docUrlColumn = headers.indexOf('Посилання на акт');
  const statusColumn = headers.indexOf('Статус відправки');

  if (unloadTimeColumn === -1 || docUrlColumn === -1 || statusColumn === -1) {
    Logger.log('Не знайдено необхідних стовпців');
    return;
  }

  const now = new Date();

  for (let i = 1; i < data.length; i++) {
    const status = data[i][statusColumn]; // Отримуємо статус відправки

    if (status !== 'Відправлено') { // Перевіряємо чи статус не "Відправлено"
      const unloadTime = new Date(data[i][unloadTimeColumn]); // Отримуємо дату і час завершення розвантаження
      const documentUrl = data[i][docUrlColumn]; // Отримуємо посилання на акт

      if (unloadTime <= now && documentUrl) { // Перевіряємо чи настав час відправки
        const documentId = documentUrl.split('/d/')[1].split('/')[0]; // Отримуємо ID файлу з URL
        sendActDocumentToTelegram(documentId, CHAT_ID_ACT);
        // Оновлюємо статус відправки
        sheet.getRange(i + 1, statusColumn + 1).setValue('Відправлено');
      }
    }
  }
}

// Допоміжна функція для форматування дати і часу в форматі dd.MM.yyyy
function formatDateTime_ddMMyyyy(dateTime) {
  const day = String(dateTime.getDate()).padStart(2, '0');
  const month = String(dateTime.getMonth() + 1).padStart(2, '0');
  const year = dateTime.getFullYear();
  return `${day}.${month}.${year}`;
}

// Допоміжна функція для форматування дати і часу
function formatDateTime(dateTimeStr) {
  try {
    // Перетворення рядка на об'єкт Date
    const dateObj = new Date(dateTimeStr);

    // Отримуємо частини дати
    const day = String(dateObj.getDate()).padStart(2, '0');
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const year = dateObj.getFullYear();

    // Отримуємо частини часу
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');

    // Форматуємо дату та час
    return `${day}.${month}.${year} року о ${hours} год. ${minutes} хв.`;
  } catch (error) {
    return dateTimeStr;  // Повертаємо оригінальний рядок, якщо сталася помилка
  }
}

// Допоміжна функція для розрахунку часу закінчення
function calculateEndTime(dateTimeStr) {
  // Створюємо об'єкт Date з вхідного рядка
  const dateObj = new Date(dateTimeStr);
  // Генеруємо випадкове значення в діапазоні від 40 до 55 з кроком 5
  const randomMinutes = Math.floor(Math.random() * 4) * 5 + 40;
  // Додаємо випадкові хвилини до поточного часу
  dateObj.setMinutes(dateObj.getMinutes() + randomMinutes);
  return dateObj;
}

// Допоміжна функція для округлення часу до найближчих 5 хвилин у майбутньому і додавання 5 хвилин
function roundUpToNearest5MinutesAndAdd5(date) {
  const dateCopy = new Date(date.getTime()); // Працюємо з копією дати
  const minutes = dateCopy.getMinutes();
  const remainder = minutes % 5;
  const minutesToAdd = remainder === 0 ? 5 : (5 - remainder) + 5;
  dateCopy.setMinutes(minutes + minutesToAdd);
  dateCopy.setSeconds(0, 0); // Обнуляємо секунди і мілісекунди
  return dateCopy;
}

// функція що рахує густину через різницю температур
function calculateDensityAtUnload(tempLoad, tempUnload, densityAtLoad, densityAt15) {
  // Перевіряємо вхідні дані
  if (!isFinite(tempLoad) || !isFinite(tempUnload) || !isFinite(densityAtLoad) || !isFinite(densityAt15)) {
    console.error('Всі параметри мають бути числами');
    return null;
  }

  try {
    // Якщо температура завантаження 15°C, густина при розвантаженні дорівнює густині при 15°C
    if (tempLoad === 15) {
      return Math.round(densityAt15 * 10) / 10;
    }

    // Розраховуємо коефіцієнт на основі даних завантаження
    const beta = (1 - (densityAtLoad / densityAt15)) / (tempLoad - 15);

    // Розраховуємо нову густину при температурі розвантаження
    const newDensity = densityAt15 * (1 - beta * (tempUnload - 15));

    // Заокруглюємо до 1 знаку після коми
    return Math.round(newDensity * 10) / 10;
  } catch (error) {
    console.error('Помилка розрахунку:', error);
    return null;
  }
}

// функція що рахує обєм від маси і густини
function calculateVolume(mass, density) {
  const volumeInCubicMeters = mass / density; // Об'єм у м³
  return Math.round(volumeInCubicMeters * 1000); // Переводимо в літри і заокруглюємо до цілого
}

// Функція для отримання поточної температур
function getCurrentTemperature(city) {
  const apiKey = '947e93e29702465ec4320be84116a404'; // Замініть на свій API-ключ
  const apiUrl = `https://api.openweathermap.org/data/2.5/weather?q=${city}&units=metric&appid=${apiKey}`;

  try {
    const response = UrlFetchApp.fetch(apiUrl);
    const data = JSON.parse(response.getContentText());
    const temperature = data.main.temp; // Поточна температур в градусах Цельсія
    return temperature.toFixed(1); // Заокруглено до одного знака після коми
  } catch (error) {
    Logger.log('Помилка при отриманні температур: ' + error.message);
    return 'Дані недоступні';
  }
}

// Функція для ручного запуску обробки
function manualRun() {
  generateResponseDocument(null); // Передаємо null для ручного запуску
}

// Функція для перевірки і відправки документів в Telegram кожні 5 хвилин
function checkAndSendDocumentsTrigger() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(ACT_SHEET_NAME);
  const data = sheet.getDataRange().getValues();
  const headers = data[0];
  const unloadTimeColumn = headers.indexOf('Дата і час завершення розвантаження');
  const docUrlColumn = headers.indexOf('Посилання на акт');
  const statusColumn = headers.indexOf('Статус відправки');

  if (unloadTimeColumn === -1 || docUrlColumn === -1 || statusColumn === -1) {
    Logger.log('Не знайдено необхідних стовпців');
    return;
  }

  const now = new Date();

  for (let i = 1; i < data.length; i++) {
    const status = data[i][statusColumn]; // Отримуємо статус відправки

    if (status !== 'Відправлено') { // Перевіряємо чи статус не "Відправлено"
      const unloadTime = new Date(data[i][unloadTimeColumn]); // Отримуємо дату і час завершення розвантаження
      const documentUrl = data[i][docUrlColumn]; // Отримуємо посилання на акт

      if (unloadTime <= now && documentUrl) { // Перевіряємо чи настав час відправки
        const documentId = documentUrl.split('/d/')[1].split('/')[0]; // Отримуємо ID файлу з URL
        sendActDocumentToTelegram(documentId, CHAT_ID_ACT);
        // Оновлюємо статус відправки
        sheet.getRange(i + 1, statusColumn + 1).setValue('Відправлено');
      }
    }
  }
}

// Функція для автоматичного заповнення даних про машину
function autoFillVehicleData(autoNumbers) {
  try {
    Logger.log('Початок пошуку даних для транспорту: ' + autoNumbers.join(' '));

    // Відкриваємо обидва аркуші
    const sourceSheet = SpreadsheetApp.openById(SHEET_ID).getSheetByName('Документи на машини');
    const targetSheet = SpreadsheetApp.openById(ACT_SHEET_ID).getSheetById(1678395711);

    // Отримуємо всі дані з вихідного аркуша
    const sourceData = sourceSheet.getDataRange().getValues();
    const sourceHeaders = sourceData[0];

    // Отримуємо індекс колонки з номером транспорту
    const vehicleNumberIndex = sourceHeaders.indexOf('Номер автомобіля і причепа');

    // Шукаємо останній рядок з даними про цей транспорт
    let foundRowIndex = -1;
    const vehicleNumberStr = autoNumbers.join(' ');

    for (let i = sourceData.length - 1; i >= 1; i--) {
      if (sourceData[i][vehicleNumberIndex] === vehicleNumberStr) {
        foundRowIndex = i;
        break;
      }
    }

    if (foundRowIndex === -1) {
      Logger.log('Дані про транспорт не знайдено');
      return;
    }

    // Отримуємо дані про знайдений транспорт
    const vehicleData = sourceData[foundRowIndex];

    // Отримуємо заголовки цільового аркуша
    const targetHeaders = targetSheet.getRange(1, 1, 1, targetSheet.getLastColumn()).getValues()[0];
    const lastRow = targetSheet.getLastRow();

    // Мапінг даних між аркушами
    const dataMapping = {
      'Номери пломб': {
        sourceColumn: 'Номери пломб',
        targetColumns: ['Номер першої пломби', 'Номер останньої пломби'],
        transform: (value) => {
          const seals = value.split('-');
          return {
            'Номер першої пломби': seals[0],
            'Номер останньої пломби': seals[1]
          };
        }
      },
      'Тип палива': {
        sourceColumn: 'Тип палива',
        targetColumn: 'Найменування вантажу'
      },
      'Об\'єм (л)': {
        sourceColumn: 'Об\'єм (л)',
        targetColumn: 'Об\'єм відвантаження л.'
      },
      'Густина (кг/м³)': {
        sourceColumn: 'Густина (кг/м³)',
        targetColumn: 'Густина відвантаження кг/м³'
      },
      'Густина при 15°C': {
        sourceColumn: 'Густина при 15°C',
        targetColumn: 'Густина відвантаження при температурі 15 С° кг/м³'
      },
      'Температура': {
        sourceColumn: 'Температура',
        targetColumn: 'Температура відвантаження С°'
      },
      'Вага (кг)': {
        sourceColumn: 'Вага (кг)',
        targetColumn: 'Маса відвантаження кг.'
      },
      // --- Додано для перенесення Суми та Об'єму 15C ---
      'Сума $': {
        sourceColumn: 'Сума $',
        targetColumn: 'Сума $' // Переконайтесь, що колонка з такою назвою існує в аркуші "Розмитнення"
      },
      'Об\'єм при 15°C (л)': {
        sourceColumn: 'Об\'єм при 15°C (л)',
        targetColumn: 'Об\'єм при 15°C (л)' // Переконайтесь, що колонка з такою назвою існує в аркуші "Розмитнення"
      },
      'RMPD1': {
        sourceColumn: 'RMPD1',
        targetColumn: 'RMPD1'
      },
      'RMPD2': {
        sourceColumn: 'RMPD2',
        targetColumn: 'RMPD2'
      },
      'Номер CMR': {
        sourceColumn: 'Номер CMR',
        targetColumn: 'Номер CMR'
      },
      'Дата CMR': {
        sourceColumn: 'Дата CMR',
        targetColumn: 'Дата CMR'
      },
      'Номер інвойсу': {
        sourceColumn: 'Номер інвойсу',
        targetColumn: 'Номер інвойсу'
      },
      'Дата інвойсу': {
        sourceColumn: 'Дата інвойсу',
        targetColumn: 'Дата інвойсу'
      },
      // --- Кінець доданого коду ---
    };

    // Копіюємо дані в цільовий аркуш
    Object.values(dataMapping).forEach(mapping => {
      const sourceIndex = sourceHeaders.indexOf(mapping.sourceColumn);

      if (sourceIndex !== -1) {
        if (mapping.transform) {
          // Для даних, які потребують трансформації (наприклад, пломби)
          const transformedData = mapping.transform(vehicleData[sourceIndex]);
          mapping.targetColumns.forEach(targetColumn => {
            const targetIndex = targetHeaders.indexOf(targetColumn);
            if (targetIndex !== -1) {
              targetSheet.getRange(lastRow, targetIndex + 1).setValue(transformedData[targetColumn]);
              Logger.log(`Скопійовано ${targetColumn}: ${transformedData[targetColumn]}`);
            } else {
              Logger.log(`Не знайдено колонку ${targetColumn} в цільовій таблиці`);
            }
          });
        } else {
          // Для простого копіювання даних
          const targetIndex = targetHeaders.indexOf(mapping.targetColumn);
          if (targetIndex !== -1) {
            targetSheet.getRange(lastRow, targetIndex + 1).setValue(vehicleData[sourceIndex]);
            Logger.log(`Скопійовано ${mapping.targetColumn}: ${vehicleData[sourceIndex]}`);
          } else {
            Logger.log(`Не знайдено колонку ${mapping.targetColumn} в цільовій таблиці`);
          }
        }
      } else {
        Logger.log(`Не знайдено колонку ${mapping.sourceColumn} в вихідній таблиці`);
      }
    });

    // Видаляємо рядок з вихідного аркуша після успішного копіювання
    sourceSheet.deleteRow(foundRowIndex + 1); // +1 бо індекси в getRange починаються з 1

    Logger.log('Дані успішно скопійовано і видалено з вихідного аркуша');

  } catch (error) {
    Logger.log('Помилка при автоматичному заповненні даних: ' + error.toString());
  }
}
