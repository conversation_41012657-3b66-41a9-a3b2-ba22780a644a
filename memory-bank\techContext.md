# Tech Context

Проект розроблено на Google Apps Script з використанням середовища розробки VSCode. Для локальної розробки та синхронізації коду з проектом Google Apps Script використовується Clasp. Інтеграція з Google Drive здійснюється за допомогою розширених сервісів Google Drive та Docs, а також бібліотеки OAuth2 для авторизації.

**Технології:**
- Google Apps Script
- Clasp
- Google Drive Advanced Service (v3)
- Google Docs Advanced Service (v1)
- Бібліотека OAuth2 для Apps Script

**Середовище розробки:**
- VSCode

**Залежності:**
- Розширені сервіси Google: Drive (v3), Docs (v1)
- Бібліотека OAuth2 (ID: 1B7FSrk5Zi6L1rSxxTDgDEUsPzlukDsi4KGuTMorsTQHhGBzBkMun4iDF)
- Зовнішні інтеграції: Telegram Bot API, система Orlen, поштові сервіси (Gmail), системи GPS/Wialon, системи BAS/Oracle.

**Структура коду:**
- Код розділений на модулі (файли `.js`), кожен з яких відповідає за певну функціональність або інтеграцію.
- Використовуються функції верхнього рівня для організації логіки.
- Присутні тестові функції (наприклад, `testWialonToken`, `testEta`, `test_createGoodsReceiptXmlFile`).

**Структури даних та API:**
- **Google Sheets:** Використовуються як основна база даних. Дані зберігаються в різних аркушах, таких як "Розмитнення", "Резервуари", "Насоси", "Контрагенти", "Підписанти", "ІПН ФОП", "Статус ПДВ".
    - Доступ до даних здійснюється через `SpreadsheetApp.openById()` та `Spreadsheet.getRange().getValues()/setValues()`.
    - Дані про резервуари та насоси, ймовірно, зберігаються у вигляді таблиць, що містять інформацію про їхній статус (вільний/зайнятий), тип палива, час звільнення.
- **Google Forms:** Використовуються для введення даних користувачами.
    - Дані з форм обробляються функцією-обробником тригера (наприклад, `handleFuelFormSubmit`).
- **Telegram Bot API:** Взаємодія з Telegram для надсилання повідомлень та документів.
    - Використовується `UrlFetchApp.fetch()` для HTTP-запитів до API Telegram.
    - Дані передаються у форматі JSON.
- **Orlen API (припускається):** Парсинг даних з системи Orlen.
    - Ймовірно, використовується `UrlFetchApp.fetch()` для отримання даних з веб-сторінок або API Orlen.
- **Gmail API:** Читання електронних листів та вкладень.
    - Використовується `GmailApp` для доступу до поштової скриньки.
- **Google Drive API:** Створення, копіювання та керування документами.
    - Використовується `DriveApp` та `Drive.Files` (розширений сервіс) для роботи з файлами та папками.
- **Google Docs API:** Редагування вмісту документів.
    - Використовується `DocumentApp` та `Docs.Documents` (розширений сервіс) для заміни тексту в шаблонах документів.
- **Wialon API (припускається):** Отримання даних GPS та ETA.
    - Ймовірно, використовується `UrlFetchApp.fetch()` для взаємодії з API Wialon.
- **BAS/Oracle Integration:** Генерація XML файлів.
    - Дані з Google Sheets перетворюються на XML-структури для імпорту в BAS.
