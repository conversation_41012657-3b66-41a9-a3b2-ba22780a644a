function checkFormSubmission() {
    var lock = LockService.getScriptLock();
    try {
        lock.waitLock(120000); // Очікування до 2 хвилин
        // Додано "Резервуари" до списку аркушів
        const sheets = ['Розмитнення', 'SHIPPING APPLICATION', 'Резервуари']; 
        const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
        let latestTimestamp = null;
        let latestSheet = null;

        sheets.forEach(sheetName => {
            const sheet = spreadsheet.getSheetByName(sheetName);
            if (sheet) {
                const lastRow = sheet.getLastRow();
                const headerRow = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
                const timestampIndex = headerRow.indexOf('Позначка часу') + 1;
                if (timestampIndex > 0) {
                    const currentTimestamp = sheet.getRange(lastRow, timestampIndex).getValue();
                    if (!latestTimestamp || new Date(currentTimestamp) > new Date(latestTimestamp)) {
                        latestTimestamp = currentTimestamp;
                        latestSheet = sheetName;
                    }
                } else {
                    Logger.log(`Не вдалося знайти стовпець "Позначка часу" в аркуші ${sheetName}`);
                }
            }
        });

        if (latestSheet) {
            Logger.log(`Остання форма була відправлена з аркуша: ${latestSheet} о ${latestTimestamp}`);
            if (latestSheet === 'Розмитнення') {
                createResponseDocument();
                tgBotMagistr_sendNotification();// відправка в Телеграм бот сповіщень
                handleFuelFormSubmit(); // відправка в Телеграм боту повідомлень про злив пального Chupic
            } else if (latestSheet === 'SHIPPING APPLICATION') {
                // Отримуємо дані останнього рядка з аркуша SHIPPING APPLICATION
                const sheet = spreadsheet.getSheetByName('SHIPPING APPLICATION');
                const lastRow = sheet.getLastRow();
                const responses = sheet.getRange(lastRow, 1, 1, sheet.getLastColumn()).getValues()[0];
                const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
                createShippingApplication(responses, headers);
                createRMPD100(responses, headers);
            } else if (latestSheet === 'Резервуари') {
                // Якщо це форма для резервуарів, нічого не виконуємо
                Logger.log('Відповідь у формі "Резервуари" — додаткових дій не потрібно.');
            }
        } else {
            Logger.log('Не вдалося знайти останню відправлену форму.');
        }
    } catch (e) {
        Logger.log('КРИТИЧНА ПОМИЛКА в checkFormSubmission: ' + e.toString() + "\n" + e.stack);
    } finally {
        lock.releaseLock();
    }
}