function fillContactDetails() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  
  // Отримуємо аркуші "Контрагенти" та "Контактні дані"
  const contractorsSheet = ss.getSheetByName("Контрагенти");
  const contactsSheet = ss.getSheetByName("Контактні дані");
  
  // Зчитуємо дані зі стовпця A (скорочені назви контрагентів) аркуша "Контрагенти"
  const contractorsData = contractorsSheet.getRange("A2:A" + contractorsSheet.getLastRow()).getValues();

  // Зчитуємо дані з аркуша "Контактні дані" для скорочених назв, типів інформації та інформаційних даних
  const contactsData = contactsSheet.getRange("A2:E" + contactsSheet.getLastRow()).getValues();
  
  // Створюємо об'єкт для зберігання контактних даних по кожному контрагенту
  const contactInfoMap = {};

  // Проходимо по даних з аркуша "Контактні дані" та заповнюємо об'єкт contactInfoMap
  contactsData.forEach(row => {
    const contractorName = row[0]; // Скорочена назва контрагента (стовпець A)
    const infoType = row[3];       // Тип інформації (стовпець D)
    const infoData = row[4];       // Інформація (стовпець E)
    
    if (!contactInfoMap[contractorName]) {
      contactInfoMap[contractorName] = {};
    }

    // Записуємо відповідні дані в об'єкт на основі типу інформації
    if (infoType === "Юридична адреса") {
      contactInfoMap[contractorName]["address"] = infoData;
    } else if (infoType === "Телефон") {
      contactInfoMap[contractorName]["phone"] = infoData;
    } else if (infoType === "Електронна пошта") {
      contactInfoMap[contractorName]["email"] = infoData;
    }
  });

  // Заповнюємо аркуш "Контрагенти" контактною інформацією
  contractorsData.forEach((contractor, i) => {
    const contractorName = contractor[0];
    const contactInfo = contactInfoMap[contractorName] || {};

    // Вставляємо відповідні значення у стовпці G, K, L
    if (contactInfo["address"]) {
      contractorsSheet.getRange(i + 2, 7).setValue(contactInfo["address"]); // Стовпець G - Юридична адреса
    }
    if (contactInfo["phone"]) {
      contractorsSheet.getRange(i + 2, 11).setValue(contactInfo["phone"]); // Стовпець K - Телефон
    }
    if (contactInfo["email"]) {
      contractorsSheet.getRange(i + 2, 12).setValue(contactInfo["email"]); // Стовпець L - Email
    }
  });
}
