Stack trace:
Frame         Function      Args
0007FFFF9F10  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF9F10, 0007FFFF8E10) msys-2.0.dll+0x1FEBA
0007FFFF9F10  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA1E8) msys-2.0.dll+0x67F9
0007FFFF9F10  000210046832 (000210285FF9, 0007FFFF9DC8, 0007FFFF9F10, 000000000000) msys-2.0.dll+0x6832
0007FFFF9F10  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9F10  0002100690B4 (0007FFFF9F20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA1F0  00021006A49D (0007FFFF9F20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF94C4A0000 ntdll.dll
7FF94BBD0000 KERNEL32.DLL
7FF9496A0000 KERNELBASE.dll
7FF94B210000 USER32.dll
7FF94A150000 win32u.dll
7FF94BCE0000 GDI32.dll
7FF949F70000 gdi32full.dll
7FF949EC0000 msvcp_win.dll
7FF949A70000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF94A600000 advapi32.dll
7FF94B3F0000 msvcrt.dll
7FF94BD10000 sechost.dll
7FF94B520000 RPCRT4.dll
7FF948D30000 CRYPTBASE.DLL
7FF94A0B0000 bcryptPrimitives.dll
7FF94BCA0000 IMM32.DLL
