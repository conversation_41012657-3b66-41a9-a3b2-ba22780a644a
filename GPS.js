/**
 * Функція для тестування токена Wialon API.
 */
function testWialonToken() {
  // --- Налаштування ---
  const WIALON_TOKEN = "3f5d86762c6d67eb1f8c351346c86aad52A38FFE812ADAC2648D783BF7A8FF2B65068F40"; // Ваш токен

  // !!! ВАЖЛИВО: Перевірте цю URL-адресу з вашим постачальником Wialon (Transcontrol) !!!
  const WIALON_API_BASE_URL = "https://hst-api.wialon.com";;
  // Або, якщо надали іншу: const WIALON_API_BASE_URL = "https://адреса_партнера.com";

  // --- Кінець Налаштувань ---

  const service = "token/login";
  const params = {
    token: WIALON_TOKEN
  };

  // Формуємо URL для запиту
  const url = `${WIALON_API_BASE_URL}/wialon/ajax.html?svc=${service}¶ms=${encodeURIComponent(JSON.stringify(params))}`;

  Logger.log(`Спроба підключення до: ${WIALON_API_BASE_URL}`);
  Logger.log(`Використовується URL: ${url}`);

  const options = {
    'method': 'get',
    'muteHttpExceptions': true // Важливо для обробки помилок API
  };

  try {
    const response = UrlFetchApp.fetch(url, options);
    const responseCode = response.getResponseCode();
    const responseBody = response.getContentText();

    Logger.log(`HTTP статус відповіді: ${responseCode}`);
    Logger.log(`Тіло відповіді: ${responseBody}`);

    if (responseCode === 200) {
      try {
        const data = JSON.parse(responseBody);

        if (data && data.eid) {
          Logger.log("УСПІХ! Токен валідний. Отримано сесійний ID (eid): " + data.eid);
          Logger.log("Повна відповідь:");
          Logger.log(JSON.stringify(data, null, 2));
          // SpreadsheetApp.getUi().alert("Успіх! Токен валідний. Деталі дивіться в логах."); // Закоментовано для уникнення помилки контексту
        } else if (data && data.error) {
           Logger.log(`ПОМИЛКА API Wialon! Код: ${data.error}, Причина: ${data.reason || 'Не вказано'}. Перевірте токен, права доступу та URL API.`);
           // SpreadsheetApp.getUi().alert(`Помилка API Wialon ${data.error}. Токен невалідний, недостатньо прав або невірні параметри. Деталі в логах.`); // Закоментовано
        } else {
          Logger.log("ПОМИЛКА: Неочікуваний формат відповіді від сервера Wialon.");
           // SpreadsheetApp.getUi().alert("Помилка: Неочікуваний формат відповіді. Деталі в логах."); // Закоментовано
        }
      } catch (e) {
        Logger.log(`ПОМИЛКА: Не вдалося розпарсити JSON відповідь: ${e}`);
        Logger.log(`Оригінальна відповідь: ${responseBody}`);
        // SpreadsheetApp.getUi().alert(`Помилка парсингу відповіді сервера. Деталі в логах.`); // Закоментовано
      }
    } else {
      Logger.log(`ПОМИЛКА HTTP! Статус: ${responseCode}. Можливі проблеми: неправильна URL API (${WIALON_API_BASE_URL}), проблеми з мережею, або сервер Wialon недоступний.`);
      // SpreadsheetApp.getUi().alert(`Помилка HTTP ${responseCode}. Перевірте URL API та з'єднання. Деталі в логах.`); // Закоментовано
    }

  } catch (e) {
    Logger.log(`Критична помилка виконання скрипта: ${e}`);
     // SpreadsheetApp.getUi().alert(`Критична помилка скрипта: ${e}. Деталі в логах.`); // Закоментовано
  }
}