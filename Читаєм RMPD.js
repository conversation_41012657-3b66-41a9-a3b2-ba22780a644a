// Використовуємо конфігурацію з telegam_bot.js

// Конфігурація
const RMPD_CONFIRM_SHEET_ID = '1IsqVkWE0jGkfzNPiPYktxzIFCnDMF0i3IbgZcglQvt8';
const RMPD_CONFIRM_TARGET_SHEET_NAME = 'RMPD';
const RMPD_CONFIRM_RMPD_NUMBER_COL_NAME = 'Номер RMPD';
const RMPD_CONFIRM_SENDER_EMAIL = '<EMAIL>';
const RMPD_CONFIRM_PROCESSED_IDS_KEY = 'RMPD_CONFIRM_PROCESSED_IDS_KEY';

/**
 * Головна функція для пошуку та обробки листів з підтвердженнями RMPD за ПОТОЧНИЙ ДЕНЬ.
 * Рекомендується налаштувати тригер для автоматичного запуску цієї функції (наприклад, щогодини або частіше).
 */
function processRmpdConfirmations() {
  try {
    Logger.log('--- Запуск обробки підтверджень RMPD (за сьогодні) ---');

    const scriptProperties = PropertiesService.getScriptProperties();
    const processedIds = JSON.parse(scriptProperties.getProperty(RMPD_CONFIRM_PROCESSED_IDS_KEY) || '[]');

    Logger.log('Кількість оброблених RMPD ID: ' + processedIds.length);

    // --- Формуємо пошуковий запит на сьогодні ---
    const today = new Date();
    // Встановлюємо час на початок дня (00:00:00) для форматування
    today.setHours(0, 0, 0, 0);
    const formattedToday = Utilities.formatDate(today, Session.getScriptTimeZone(), "yyyy/MM/dd");
    // Шукаємо листи, отримані ПІСЛЯ початку сьогоднішнього дня
    const searchQuery = `from:${RMPD_CONFIRM_SENDER_EMAIL} has:attachment filename:xml after:${formattedToday}`;
    // --- Кінець формування запиту ---

    Logger.log('Пошуковий запит RMPD (за сьогодні): ' + searchQuery);

    const threads = GmailApp.search(searchQuery);
    Logger.log(`Знайдено ${threads.length} ланцюжків з підтвердженнями RMPD за сьогодні`);

    let newProcessedIds = [...processedIds]; // Копіюємо масив оброблених ID
    let processedCount = 0;
    let failedCount = 0;

    for (let thread of threads) {
      const messages = thread.getMessages();
      for (let message of messages) {
        const messageId = message.getId();

        // Перевіряємо, чи не обробляли цей лист раніше
        if (!processedIds.includes(messageId)) {
          Logger.log(`Обробка нового RMPD повідомлення ID: ${messageId} (Дата: ${message.getDate()})`);
          const success = processRmpdEmail(message); // processRmpdEmail залишається без змін
          if (success) {
            newProcessedIds.push(messageId); // Додаємо ID до оброблених тільки якщо обробка УСПІШНА
            processedCount++;
          } else {
             Logger.log(`Помилка обробки повідомлення ID: ${messageId}. ID не буде додано до оброблених.`);
             failedCount++;
          }
          // Додаємо невелику паузу між обробкою листів, щоб уникнути перевищення лімітів
          Utilities.sleep(500);
        } else {
          // Logger.log(`Пропуск вже обробленого RMPD повідомлення ID: ${messageId}`);
        }
      }
    }

    // Зберігаємо оновлений список оброблених повідомлень, якщо були нові УСПІШНО оброблені
    if (processedCount > 0) {
       scriptProperties.setProperty(RMPD_CONFIRM_PROCESSED_IDS_KEY, JSON.stringify(newProcessedIds));
       Logger.log(`Збережено ${newProcessedIds.length} оброблених RMPD ID.`);
    }

    // Зберігання часу останньої перевірки більше не потрібне для логіки пошуку

    Logger.log(`--- Обробку підтверджень RMPD за сьогодні завершено. Оброблено ${processedCount} нових листів. Не вдалося обробити: ${failedCount}. Загальна кількість оброблених ID: ${newProcessedIds.length} ---`);

  } catch (error) {
    Logger.log('Критична помилка в processRmpdConfirmationsToday: ' + error.toString() + "\n" + error.stack);
  }
}

/**
 * Обробляє вкладення конкретного листа.
 * @param {GoogleAppsScript.Gmail.GmailMessage} message Об'єкт листа.
 * @return {boolean} true, якщо обробка пройшла успішно, інакше false.
 */
function processRmpdEmail(message) {
  try {
    const attachments = message.getAttachments();
    let xmlAttachment = null;
    let htmlAttachment = null;

    // Шукаємо XML і HTML вкладення
    for (let attachment of attachments) {
      const fileName = attachment.getName().toLowerCase();
      if (fileName.endsWith('.xml')) {
        xmlAttachment = attachment;
      } else if (fileName.endsWith('.html')) {
        htmlAttachment = attachment;
      }
    }

    if (!xmlAttachment) {
      Logger.log(`XML вкладення не знайдено в листі ID: ${message.getId()}`);
      return false;
    }

    const xmlContent = xmlAttachment.getDataAsString('UTF-8');
    const parsedData = parseRmpdConfirmationXml(xmlContent);

    if (parsedData) {
      // Обробляємо HTML вкладення, якщо воно є
      if (htmlAttachment) {
        const htmlContent = htmlAttachment.getDataAsString();
        const blob = Utilities.newBlob(htmlContent, 'text/html', `RMPD_${parsedData.rmpdNumber}.html`);

        // Конвертуємо HTML в PDF
        const pdf = blob.getAs('application/pdf');
        pdf.setName(`RMPD_${parsedData.rmpdNumber}.pdf`);

        // Зберігаємо PDF в Google Drive
        const folder = DriveApp.getFolderById('1I7F3awfTV3TEgD8BYof5tbevOXoky_P2');
        const pdfFile = folder.createFile(pdf);

        // Відправляємо в телеграм
        sendRmpdToTelegram(pdfFile.getId(), parsedData.truckNumber, parsedData.trailerNumber, parsedData);
      }

      return upsertRmpdDataToSheet(parsedData);
    } else {
      Logger.log(`Не вдалося розпарсити XML з листа ID: ${message.getId()}`);
      return false;
    }

  } catch (error) {
    Logger.log(`Помилка при обробці листа ID ${message.getId()}: ${error.toString()}`);
    return false;
  }
}

/**
 * Відправляє PDF файл RMPD у відповідну телеграм групу водія
 * @param {string} fileId ID файлу на Google Drive
 * @param {string} truckNumber Номер тягача
 * @param {string} trailerNumber Номер причепа
 * @param {object} parsedData Розпарсені дані з XML
 */
function sendRmpdToTelegram(fileId, truckNumber, trailerNumber, parsedData) {
  try {
    // Формуємо комбінований номер для пошуку
    const combinedNumber = `${truckNumber.trim()} ${trailerNumber.trim()}`;

    // Використовуємо функцію з telegam_bot.js для пошуку chat_id
    const chatId = tgBotMagistr_findChatIdByCombinedNumber(combinedNumber);
    // const chatId = '276789696';// чат зі мною

    if (!chatId) {
      Logger.log(`Не знайдено chat_id для авто ${truckNumber} ${trailerNumber}`);
      return;
    }

    // Відправляємо у відповідний чат
    return sendRmpdMessageToChat(fileId, truckNumber, trailerNumber, parsedData, chatId);

  } catch (error) {
    Logger.log(`Помилка відправки RMPD в Telegram: ${error.toString()}`);
  }
}

/**
 * Парсить XML-вміст підтвердження RMPD_110 та витягує необхідні дані.
 * @param {string} xmlContent Вміст XML файлу.
 * @return {object|null} Об'єкт з даними або null у разі помилки.
 */
function parseRmpdConfirmationXml(xmlContent) {
  try {
    const document = XmlService.parse(xmlContent);
    const root = document.getRootElement();
    const ns = root.getNamespace();
    const ns2 = XmlService.getNamespace('ns2', 'http://www.mf.gov.pl/RMPD/2024/03/18/PTypes.xsd');

    const getText = (element, childName, namespace) => element?.getChild(childName, namespace)?.getText()?.trim() || '';

    const rmpdNumber = getText(root, 'RmpdNumber', ns);

    const registeredInfo = root.getChild('RegisteredRmpdInfo', ns);
    const creationDate = getText(registeredInfo, 'CreationDate', ns2);

    const permissionInfo = root.getChild('PermissionInfo', ns);
    const startTransportDate = getText(permissionInfo, 'StartTransportDate', ns2);
    const endTransportDate = getText(permissionInfo, 'EndTransportDate', ns2);
    const loaded = getText(permissionInfo, 'Loaded', ns2);
    const journeyDirection = getText(permissionInfo, 'JourneyDirection', ns2);

    const startEndPlaceJourney = permissionInfo?.getChild('StartEndPlaceJourney', ns2);
    const entranceToPoland = startEndPlaceJourney?.getChild('EntranceToPoland', ns2);
    const exitFromPoland = startEndPlaceJourney?.getChild('ExitFromPoland', ns2);
    const entranceRoutePlace = getText(entranceToPoland, 'RoutePlace', ns2);
    const exitRoutePlace = getText(exitFromPoland, 'RoutePlace', ns2);

    const meansOfTransport = root.getChild('MeansOfTransport', ns);
    const truckNumber = getText(meansOfTransport, 'TruckNumber', ns2);
    const trailerNumber = getText(meansOfTransport, 'TrailerNumber', ns2);

    if (rmpdNumber && truckNumber && trailerNumber) {
      return {
        rmpdNumber: rmpdNumber,
        truckNumber: truckNumber,
        trailerNumber: trailerNumber,
        creationDate: creationDate,
        startTransportDate: startTransportDate,
        endTransportDate: endTransportDate,
        loaded: loaded,
        journeyDirection: journeyDirection,
        entranceRoutePlace: entranceRoutePlace,
        exitRoutePlace: exitRoutePlace
      };
    } else {
      Logger.log(`Не вдалося знайти основні теги в XML: RMPD=${rmpdNumber}, Авто=${truckNumber}, Причіп=${trailerNumber}`);
      return null;
    }
  } catch (error) {
    Logger.log(`Помилка парсингу XML: ${error.toString()}`);
    return null;
  }
}

/**
 * Додає або оновлює рядок в Google Таблиці "RMPD" на основі номера RMPD.
 * @param {object} data Об'єкт з даними, отриманими з parseRmpdConfirmationXml.
 * @return {boolean} true, якщо операція пройшла успішно, інакше false.
 */
function upsertRmpdDataToSheet(data) {
  try {
    const sheet = SpreadsheetApp.openById(RMPD_CONFIRM_SHEET_ID).getSheetByName(RMPD_CONFIRM_TARGET_SHEET_NAME);
    if (!sheet) {
      Logger.log(`Аркуш "${RMPD_CONFIRM_TARGET_SHEET_NAME}" не знайдено в таблиці ID ${RMPD_CONFIRM_SHEET_ID}`);
      return false;
    }

    const sheetData = sheet.getDataRange().getValues();
    const headers = sheetData[0];

    const rmpdColIndex = headers.indexOf(RMPD_CONFIRM_RMPD_NUMBER_COL_NAME);
    if (rmpdColIndex === -1) {
      Logger.log(`Стовпець "${RMPD_CONFIRM_RMPD_NUMBER_COL_NAME}" не знайдено в аркуші "${RMPD_CONFIRM_TARGET_SHEET_NAME}".`);
      return false;
    }

    let foundRowIndex = -1;
    for (let i = 1; i < sheetData.length; i++) {
      // Переконуємось, що порівнюємо як рядки, щоб уникнути проблем з типами
      if (String(sheetData[i][rmpdColIndex]).trim() === String(data.rmpdNumber).trim()) {
        foundRowIndex = i + 1;
        Logger.log(`Знайдено існуючий рядок ${foundRowIndex} для RMPD ${data.rmpdNumber}`);
        break;
      }
    }

    const rowData = [
      data.rmpdNumber,
      `${data.truckNumber} ${data.trailerNumber}`,
      formatDateFromISO(data.creationDate),
      formatDateFromISO(data.startTransportDate),
      formatDateFromISO(data.endTransportDate),
      data.loaded?.trim() === 'true' ? 'так' : 'ні',
      data.entranceRoutePlace,
      data.exitRoutePlace
    ];

    // Перевірка відповідності кількості стовпців
     if (rowData.length !== headers.length) {
       Logger.log(`УВАГА: Кількість підготовлених даних (${rowData.length}) не відповідає кількості стовпців (${headers.length}) в аркуші "${RMPD_CONFIRM_TARGET_SHEET_NAME}". Будуть записані лише перші ${Math.min(rowData.length, headers.length)} стовпців.`);
       Logger.log(`Заголовки: ${JSON.stringify(headers)}`);
       Logger.log(`Дані для запису: ${JSON.stringify(rowData)}`);
     }
     const numColsToWrite = Math.min(rowData.length, headers.length);

    if (foundRowIndex !== -1) {
      sheet.getRange(foundRowIndex, 1, 1, numColsToWrite).setValues([rowData.slice(0, numColsToWrite)]);
      Logger.log(`Рядок ${foundRowIndex} успішно оновлено для RMPD ${data.rmpdNumber}`);
    } else {
      sheet.appendRow(rowData.slice(0, numColsToWrite));
      Logger.log(`Новий рядок успішно додано для RMPD ${data.rmpdNumber}`);
    }
    return true;

  } catch (error) {
    Logger.log(`Помилка при записі/оновленні даних в аркуш "${RMPD_CONFIRM_TARGET_SHEET_NAME}": ${error.toString()}\nДані: ${JSON.stringify(data)}`);
    return false;
  }
}

/**
 * Допоміжна функція для форматування дати з ISO формату.
 * @param {string} isoDateString Дата у форматі ISO 8601.
 * @return {string} Дата у форматі "yyyy-MM-dd HH:mm:ss" або "yyyy-MM-dd", або порожній рядок.
 */
function formatDateFromISO(isoDateString) {
  if (!isoDateString) return '';
  try {
    let date = new Date(isoDateString);
    if (isNaN(date.getTime())) {
       const datePart = isoDateString.split('+')[0].split('T')[0]; // Спробуємо взяти лише дату YYYY-MM-DD
       date = new Date(datePart);
       // Якщо дата містить час, то повернемо Invalid Date, інакше спробуємо ще раз
       if (isNaN(date.getTime())) {
           Logger.log(`Не вдалося розпізнати дату: ${isoDateString}`);
           return isoDateString;
       }
       // Якщо успішно розпізнали лише дату, форматуємо без часу
       return Utilities.formatDate(date, Session.getScriptTimeZone(), "yyyy-MM-dd");
    }

    // Якщо розпізнали з часом, перевіряємо, чи є 'T' в оригіналі
    if (isoDateString.includes('T')) {
      return Utilities.formatDate(date, Session.getScriptTimeZone(), "yyyy-MM-dd HH:mm:ss");
    } else {
      // Якщо 'T' немає, але дата валідна, повертаємо лише дату
      return Utilities.formatDate(date, Session.getScriptTimeZone(), "yyyy-MM-dd");
    }
  } catch (e) {
    Logger.log(`Помилка форматування дати "${isoDateString}": ${e}`);
    return isoDateString;
  }
}

// Допоміжні функції

/**
 * Допоміжна функція для очищення історії обробки підтверджень RMPD.
 * Запускати вручну за потреби.
 */
function clearRmpdConfirmHistory() {
  try {
    const scriptProperties = PropertiesService.getScriptProperties();
    // Видаляємо лише ключ зі списком оброблених ID
    scriptProperties.deleteProperty(RMPD_CONFIRM_PROCESSED_IDS_KEY);
    Logger.log(`Історію оброблених ID підтверджень RMPD (${RMPD_CONFIRM_PROCESSED_IDS_KEY}) очищено.`);
  } catch (error) {
    Logger.log('Помилка при очищенні історії підтверджень RMPD: ' + error.toString());
  }
}

/**
 * Допоміжна функція для перегляду поточного стану обробки підтверджень RMPD.
 * Запускати вручну за потреби.
 */
function checkRmpdConfirmState() {
  try {
    const scriptProperties = PropertiesService.getScriptProperties();
    const processedIds = JSON.parse(scriptProperties.getProperty(RMPD_CONFIRM_PROCESSED_IDS_KEY) || '[]');

    Logger.log('--- Стан обробки підтверджень RMPD ---');
    Logger.log(`Ключ сховища: ${RMPD_CONFIRM_PROCESSED_IDS_KEY}`);
    Logger.log('Кількість оброблених ID: ' + processedIds.length);
    // Не виводимо весь список, якщо він дуже великий
    if (processedIds.length < 100) {
        Logger.log('Оброблені ID: ' + JSON.stringify(processedIds));
    } else {
        Logger.log('Список оброблених ID занадто великий для відображення.');
    }
    Logger.log('--- Кінець стану ---');
  } catch (error) {
    Logger.log('Помилка при перевірці стану підтверджень RMPD: ' + error.toString());
  }
}

/**
 * Допоміжна функція для відправки RMPD повідомлення в конкретний чат
 * @param {string} fileId ID файлу на Google Drive
 * @param {string} truckNumber Номер тягача
 * @param {string} trailerNumber Номер причепа
 * @param {object} parsedData Розпарсені дані з XML
 * @param {string} chatId ID чату для відправки
 * @return {boolean} true якщо відправка успішна, false якщо виникла помилка
 */
function sendRmpdMessageToChat(fileId, truckNumber, trailerNumber, parsedData, chatId) {
  try {
    const file = DriveApp.getFileById(fileId);
    const blob = file.getBlob();

    // Визначаємо тип RMPD
    const isLoaded = parsedData.loaded?.trim() === 'true';
    const rmpd_type = isLoaded ? '🔄 Зворотня RMPD (завантажений)' : '🚛 Перша RMPD (порожній)';

    const caption = `${rmpd_type}\nТягач: ${truckNumber}\nПричіп: ${trailerNumber}\nRMPD: ${parsedData.rmpdNumber}`;

    const url = `https://api.telegram.org/bot${tgBotMagistr_config.botToken}/sendDocument`;
    const payload = {
      chat_id: String(chatId),
      document: blob,
      caption: caption
    };

    const options = {
      method: 'post',
      payload: payload,
      muteHttpExceptions: true
    };

    const response = UrlFetchApp.fetch(url, options);
    const responseData = JSON.parse(response.getContentText());

    if (responseData.ok) {
      return true;
    } else {
      Logger.log(`Помилка відправки в чат ${chatId}: ${responseData.description}`);
      return false;
    }

  } catch (error) {
    Logger.log(`Помилка при відправці в чат ${chatId}: ${error.toString()}`);
    return false;
  }
}
