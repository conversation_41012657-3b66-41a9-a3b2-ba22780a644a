// --- Константи для Telegram-бота (уникальні имена) ---
const BOT_TOKEN_CONTRACT = '7461294369:AAHAsQ-2bVU8hxpeW7heWeAX7UTLa20yaHk';
const API_URL_CONTRACT = `https://api.telegram.org/bot${BOT_TOKEN_CONTRACT}/sendMessage`;
const CHAT_ID_CONTRACT = '-4631002061'; // chat_id групи

function fillContractTemplate() {
  // Отримуємо активну електронну таблицю
  const ss = SpreadsheetApp.getActiveSpreadsheet();

  // Отримуємо аркуші "Реєстр договорів" і "Контрагенти"
  const contractsSheet = ss.getSheetByName("Реєстр договорів");
  const contractorsSheet = ss.getSheetByName("Контрагенти");

  // Отримуємо активний рядок і дані з нього
  const selectedRow = getSelectedRow(contractsSheet);
  const contractData = getRowData(contractsSheet, selectedRow);

    // Знаходимо індекс колонки № договору за назвою
    const contractNumberColumnIndex = findColumnIndexByName(contractsSheet, "№ договору");

      // Якщо колонку "№ договору" не знайдено, показуємо помилку і завершуємо виконання
    if (contractNumberColumnIndex === -1) {
      SpreadsheetApp.getUi().alert("Колонка '№ договору' не знайдена в аркуші 'Реєстр договорів'.");
      return;
    }

  // Скорочена назва контрагента для пошуку
  const contractorShortName = contractData[0];
    // № договору
    const contractNumber = contractData[contractNumberColumnIndex];

  // Знаходимо дані контрагента в таблиці "Контрагенти"
  const contractorInfo = findContractor(contractorsSheet, contractorShortName);

  if (!contractorInfo) {
    // Якщо контрагента не знайдено, виводимо повідомлення і припиняємо виконання
    SpreadsheetApp.getUi().alert(`Контрагент з назвою "${contractorShortName}" не знайдений у аркуші "Контрагенти".`);
    return;
  }

  // Вибір ID шаблону договору (загальний або для ФОП)
  const templateDocId = getTemplateDocId(contractorInfo);

  // ID папки, куди буде збережена копія документа
  const folderId = '1UH8mObeszBBXF7xO7QjR9Engia2Ucr8z';

  // Створюємо копію шаблону документа і отримуємо її ID
  const newDocId = createDocumentCopy(templateDocId, folderId, contractorShortName, contractNumber);

  // Відкриваємо створену копію документа для заповнення
  const newDoc = DocumentApp.openById(newDocId);
  const newBody = newDoc.getBody();

  // Підготовка плейсхолдерів для заміни та їх значень
  const placeholders = {
    '{{contractor}}': contractorInfo[1],
    '{{edrpou}}': contractorInfo[2],
    '{{IPN}}': contractorInfo[3],
    '{{contractDate}}': formatContractDate(contractData[1]),
    '{{contractNumber}}': contractData[3],
    '{{representative}}': contractorInfo[4],
    '{{signatoryName}}': contractorInfo[5],
    '{{signatoryInitials}}': formatSignatoryInitials(contractorInfo[5]),
    '{{legalAddress}}': contractorInfo[6],
    '{{bankAccount}}': contractorInfo[7],
    '{{bankMfo}}': contractorInfo[8],
    '{{bank}}': contractorInfo[9],
    '{{phone}}': contractorInfo[10],
    '{{email}}': contractorInfo[11]
  };

  // Заміна плейсхолдерів у документі
  const emptyPlaceholders = replacePlaceholders(newBody, placeholders);

  // Зберігаємо та закриваємо документ
  newDoc.saveAndClose();

  // Отримуємо посилання на документ
  const docUrl = DriveApp.getFileById(newDocId).getUrl();

  // Відправляємо посилання у Telegram-групу
  sendContractLinkToTelegram(docUrl, CHAT_ID_CONTRACT, contractorShortName, contractNumber);

  // Якщо є пусті дані, показуємо повідомлення з переліком порожніх полів
  if (emptyPlaceholders.length > 0) {
    SpreadsheetApp.getUi().alert(
      `Деякі дані залишились пустими: ${emptyPlaceholders.join(', ')}. Перевірте інформацію в таблицях.`
    );
  }

  // Повертаємо посилання на створений документ
  return newDoc.getUrl();
}

/**
 * Знаходить індекс колонки за її назвою.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet Аркуш
 * @param {string} columnName Назва колонки
 * @return {number} Індекс колонки (починаючи з 0), або -1, якщо не знайдено
 */
function findColumnIndexByName(sheet, columnName) {
  const headerRow = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
  return headerRow.indexOf(columnName);
}

// ------------------- Допоміжні функції (модифіковані) -------------------

/**
 * Створює копію шаблону документа у вказаній папці.
 * @param {string} templateDocId ID шаблону
 * @param {string} folderId ID папки
 * @param {string} contractorShortName Скорочена назва контрагента
 * @param {string} contractNumber Номер договору
 * @return {string} ID новоствореного документа
 */
function createDocumentCopy(templateDocId, folderId, contractorShortName, contractNumber) {
  const folder = DriveApp.getFolderById(folderId);
   const contractName = `${contractorShortName} - Договір №${contractNumber}`;
  return DriveApp.getFileById(templateDocId).makeCopy(contractName, folder).getId();
}

// ------------------- Допоміжні функції (не змінені) -------------------

/**
 * Отримує активний рядок з аркуша.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet Аркуш
 * @return {number} Номер активного рядка
 */
function getSelectedRow(sheet) {
  return sheet.getActiveCell().getRow();
}

/**
 * Отримує дані з рядка.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet Аркуш
 * @param {number} row Номер рядка
 * @return {Array} Масив даних рядка
 */
function getRowData(sheet, row) {
  return sheet.getRange(row, 1, 1, sheet.getLastColumn()).getValues()[0];
}

/**
 * Шукає контрагента за скороченою назвою.
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet Аркуш "Контрагенти"
 * @param {string} contractorShortName Скорочена назва контрагента
 * @return {Array|null} Дані контрагента або null, якщо не знайдено
 */
function findContractor(sheet, contractorShortName) {
  const contractorsData = sheet.getRange(2, 1, sheet.getLastRow() - 1, sheet.getLastColumn()).getValues();
  return contractorsData.find(row => row[0] === contractorShortName);
}

/**
 * Отримує ID шаблону документа залежно від типу контрагента.
 * @param {Array} contractorInfo Дані контрагента
 * @return {string} ID шаблону
 */
function getTemplateDocId(contractorInfo) {
  return contractorInfo[4] === 'Фіз. особа - підприємець'
    ? '1Xieblz5aCy_tszdt9RLkURXS6aP1gjViM9vJRJmMUNc'
    : '1vcwfsPIuoTzZAfTSb-gxr-NOPExu_Y9OY_3iTI095UY';
}

/**
 * Форматує дату договору у вигляді "день місяця рік".
 * @param {Date|string} date Дата
 * @return {string} Форматована дата
 */
function formatContractDate(date) {
  if (!(date instanceof Date)) return date;
  const months = [
    'січня', 'лютого', 'березня', 'квітня', 'травня', 'червня',
    'липня', 'серпня', 'вересня', 'жовтня', 'листопада', 'грудня'
  ];
  const day = Utilities.formatDate(date, Session.getScriptTimeZone(), "d");
  const year = Utilities.formatDate(date, Session.getScriptTimeZone(), "yyyy");
  return `${day} ${months[date.getMonth()]} ${year}`;
}

/**
 * Форматує підписанта у вигляді "Прізвище І.О.".
 * @param {string} fullName Повне ім'я
 * @return {string} Форматоване ім'я
 */
function formatSignatoryInitials(fullName) {
  if (!fullName) return '';
  const names = fullName.split(' ');
  const initials = names.slice(1).map(name => name[0] + '.').join('');
  return names[0] + ' ' + initials;
}

/**
 * Замінює плейсхолдери у документі на їх значення.
 * @param {GoogleAppsScript.Document.Body} body Тіло документа
 * @param {Object} placeholders Об'єкт плейсхолдерів
 * @return {Array} Список пустих плейсхолдерів
 */
function replacePlaceholders(body, placeholders) {
  const emptyKeys = [];
  for (const [placeholder, value] of Object.entries(placeholders)) {
    body.replaceText(placeholder, value || '');
    if (!value) {
      emptyKeys.push(placeholder);
    }
  }
  return emptyKeys;
}

/**
 * Відправляє посилання на документ у Telegram-групу.
 * @param {string} docUrl Посилання на Google Doc
 * @param {string} chatId ID чату Telegram
 * @param {string} contractorShortName Назва контрагента
 * @param {string} contractNumber Номер договору
 */
function sendContractLinkToTelegram(docUrl, chatId, contractorShortName, contractNumber) {
  if (!docUrl || !chatId) {
    Logger.log('Відсутні параметри для відправки посилання');
    return;
  }

  const message = `Договір для ${contractorShortName} №${contractNumber} доступний за посиланням:\n${docUrl}`;

  const payload = {
    chat_id: chatId,
    text: message
  };

  const options = {
    method: 'post',
    payload: payload,
    muteHttpExceptions: true
  };

  try {
    const response = UrlFetchApp.fetch(API_URL_CONTRACT, options);
    Logger.log('Відповідь Telegram: ' + response.getContentText());
  } catch (error) {
    Logger.log('Помилка відправки у Telegram: ' + error);
  }
}