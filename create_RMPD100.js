const RMPD_SHEET_ID = '1IsqVkWE0jGkfzNPiPYktxzIFCnDMF0i3IbgZcglQvt8'; // ID таблиці
const RMPD_SHEET_NAME = 'SHIPPING APPLICATION'; // назва аркуша
const RMPD_REGISTRY_NAME = 'Реєстр водіїв і автомобілів'; // назва аркуша
const RMPD_FOLDER_ID = '1WjI5YyynvKQDYe0mHWty3QiDULtkPIIt'; // ID папки
const CARRIER_EMAIL = '<EMAIL>'; // Adres e-mail przewoźnika

// Telegram configuration
const BOT_TOKEN_RMPD = '**********************************************';// Токен <PERSON>ik
// const CHAT_ID_RMPD = '276789696';// чат зі мною
const CHAT_ID_RMPD = '-**********'; // chat_id групи
const API_URL = `https://api.telegram.org/bot${BOT_TOKEN_RMPD}/sendDocument`;

function createRMPD100(responses, headers) {
    const MAX_RETRIES = 3; // Максимальна кількість спроб
    const RETRY_DELAY_MS = 5000; // Затримка між спробами в мілісекундах (5 секунди)

    for (let attempt = 0; attempt < MAX_RETRIES; attempt++) {
        try {
            // Отримуємо необхідні дані з таблиці
            const deliveryDate = formatDate_yyyyMMdd(responses[headers.indexOf('Дата доставки транспортних засобів')]);
            const vehicleTrailerCombined = responses[headers.indexOf('Номер автомобіля і причепа')]; // Зчитуємо номер автомобіля і причепа
            Logger.log(`vehicleTrailerCombined: ${vehicleTrailerCombined}`);
            const borderCrossingPoint = responses[headers.indexOf('Пункт перетину кордону')]; // Зчитуємо пункт перетину

            Logger.log(`Створення RMPD100 для транспорту: ${vehicleTrailerCombined}, дата доставки: ${deliveryDate}`);

            // Отримуємо дані транспорту з реєстру
            const vehicleInfo = findVehicleInfo(vehicleTrailerCombined);
            // Перевіряємо, чи знайдено інформацію про транспорт
            if (!vehicleInfo) {
                throw new Error(`Не знайдено інформацію про транспорт: ${vehicleTrailerCombined} в реєстрі`);
            }

            const endDate = new Date(new Date(deliveryDate).getTime() + 10 * 24 * 60 * 60 * 1000);
            // форматуємо дату кінця доставки
            const date_end = formatDate_yyyyMMdd(endDate);

            // Генеруємо XML на основі шаблону
            const xmlContent = generateXML({
                startDate: deliveryDate,
                endDate: date_end,
                vehicleInfo: vehicleInfo,
                borderCrossing: borderCrossingPoint // Передаємо пункт перетину
            });

            // Зберігаємо та відправляємо файл
            const fileName = `RMPD100_${vehicleTrailerCombined}.xml`;
            const file = DriveApp.getFolderById(RMPD_FOLDER_ID).createFile(fileName, xmlContent, 'application/xml'); // створюємо файл
            sendRMPDDocumentToTelegram(file.getId(), CHAT_ID_RMPD);

            Logger.log(`RMPD100 успішно створено для транспорту: ${vehicleTrailerCombined}`);
            return; // Успішно виконано, виходимо з функції

        } catch (error) {
            Logger.log(`Спроба ${attempt + 1}/${MAX_RETRIES}: Помилка при створенні RMPD100: ${error.toString()}`);

            // Якщо це не остання спроба, робимо затримку перед наступною спробою
            if (attempt + 1 < MAX_RETRIES) {
                Logger.log(`Очікування ${RETRY_DELAY_MS/1000} секунд перед наступною спробою...`);
                Utilities.sleep(RETRY_DELAY_MS);
            } else {
                Logger.log(`Всі ${MAX_RETRIES} спроб створення RMPD100 були невдалими.`);
            }
        }
    }
}
// Функція для пошуку інформації про автомобіль у реєстрі
// на основі номера автомобіля і причепа
function findVehicleInfo(vehicleTrailerCombined) {
    try {
        if (!vehicleTrailerCombined) {
            Logger.log('ПОМИЛКА: vehicleTrailerCombined є порожнім або undefined');
            return null;
        }

        const sheet = SpreadsheetApp.openById(RMPD_SHEET_ID).getSheetByName(RMPD_REGISTRY_NAME);
        if (!sheet) {
            Logger.log(`ПОМИЛКА: Не вдалося знайти аркуш "${RMPD_REGISTRY_NAME}" в таблиці`);
            return null;
        }

        const data = sheet.getDataRange().getValues();
        const headers = data[0];

        // Розділяємо рядок на номер автомобіля та номер причепа
        const parts = vehicleTrailerCombined.split(' ');
        if (parts.length < 2) {
            Logger.log(`ПОМИЛКА: Неправильний формат номера автомобіля і причепа: "${vehicleTrailerCombined}". Очікується формат "НОМЕР_АВТО НОМЕР_ПРИЧЕПА"`);
            return null;
        }

        const [searchCarNumber, searchTrailerNumber] = parts;

        const carNumberIndex = headers.indexOf('№ Автомобіля');
        const trailerNumberIndex = headers.indexOf('№ причепа');
        const gpsTrackerIndex = headers.indexOf('№ GPS Трекера');
        const gpsTracker2Index = headers.indexOf('№ GPS Трекера 2');

        if (carNumberIndex === -1 || trailerNumberIndex === -1) {
            Logger.log(`ПОМИЛКА: Не знайдено необхідні колонки в реєстрі. carNumberIndex=${carNumberIndex}, trailerNumberIndex=${trailerNumberIndex}`);
            return null;
        }

        Logger.log(`Пошук транспорту в реєстрі: авто="${searchCarNumber}", причіп="${searchTrailerNumber}"`);

        for (let i = 1; i < data.length; i++) {
            const registryCarNumber = data[i][carNumberIndex];
            const registryTrailerNumber = data[i][trailerNumberIndex];

            // Перевіряємо співпадіння номерів автомобіля та причепа
            if (registryCarNumber === searchCarNumber && registryTrailerNumber === searchTrailerNumber) {
                Logger.log(`Знайдено транспорт в реєстрі: рядок ${i+1}`);
                return {
                    truckNumber: registryCarNumber,
                    trailerNumber: registryTrailerNumber,
                    geoLocator: data[i][gpsTrackerIndex] || '',  // Primary GPS tracker
                    geoLocator2: data[i][gpsTracker2Index] || '' // Secondary GPS tracker
                };
            }
        }

        Logger.log(`УВАГА: Транспорт не знайдено в реєстрі: авто="${searchCarNumber}", причіп="${searchTrailerNumber}"`);
        return null;
    } catch (error) {
        Logger.log(`ПОМИЛКА у функції findVehicleInfo: ${error.toString()}`);
        return null;
    }
}

function generateXML(data) {
    // Перевірка наявності необхідних даних
    if (!data || !data.vehicleInfo) {
        throw new Error('Відсутні необхідні дані для генерації XML. Перевірте vehicleInfo.');
    }

    // Визначаємо номер дороги для в'їзду на основі пункту перетину
    let routePlaceEntrance = data.borderCrossing || "Hrebenne - Rawa Ruska"; // Значення за замовчуванням, якщо не вказано
    let routeNumberEntrance = "DK17"; // Номер дороги за замовчуванням

    if (routePlaceEntrance === "Dorohusk - Jogodzin") {
        routeNumberEntrance = "DK12";
    } else if (routePlaceEntrance === "Hrebenne - Rawa Ruska") {
        routeNumberEntrance = "DK17";
    } else if (routePlaceEntrance === "Zosin - Ustiług") {
        routeNumberEntrance = "S74";
    } else if (routePlaceEntrance === "Barwinek - Vyżny Komarnik") {
        routeNumberEntrance = "DK19";
    } else {
        // Обробка невідомого значення - можна залишити за замовчуванням або додати логування
        Logger.log(`Невідомий пункт перетину кордону: ${routePlaceEntrance}. Використовується значення за замовчуванням.`);
        routePlaceEntrance = "Hrebenne - Rawa Ruska"; // Повертаємо до значення за замовчуванням
        routeNumberEntrance = "DK17";
    }

    return `<?xml version="1.0" encoding="UTF-8"?>
<p:RMPD_100 xmlns:p="http://www.mf.gov.pl/RMPD/2024/03/18/RMPD_100.xsd"
            xmlns:tp="http://www.mf.gov.pl/RMPD/2024/03/18/PTypes.xsd">
    <!-- Базова інформація про перевізника -->
    <p:Carrier>
        <tp:TraderInfo>
            <tp:IdSisc>FRUA1215779550001</tp:IdSisc>
            <tp:TraderName>MAGISTR TRANS LLC</tp:TraderName>
            <tp:TraderIdentityType>INNY</tp:TraderIdentityType>
            <tp:TraderIdentityNumber>UA45561434</tp:TraderIdentityNumber>
        </tp:TraderInfo>
        <tp:TraderAddress>
            <tp:Street>9 RUE str. Promyslova</tp:Street>
            <tp:HouseNumber>BRAK</tp:HouseNumber>
            <tp:City>Plotycha village</tp:City>
            <tp:Country>UA</tp:Country>
            <tp:PostalCode>47704</tp:PostalCode>
        </tp:TraderAddress>
    </p:Carrier>

    <p:PermissionInfo>
        <tp:StartTransportDate>${data.startDate}</tp:StartTransportDate>
        <tp:EndTransportDate>${data.endDate}</tp:EndTransportDate>
        <tp:CountryLoadCode>UA</tp:CountryLoadCode>
        <tp:CountryUnloadCode>LT</tp:CountryUnloadCode>
        <tp:Loaded>false</tp:Loaded>
        <tp:TypeOfRoadTransport>TRANSIT_TRANSPORT</tp:TypeOfRoadTransport>
        <tp:TypeOfPermission>NOT_OBLIGED</tp:TypeOfPermission>
        <tp:NotObligedLegalBase>094</tp:NotObligedLegalBase>
        <tp:JourneyDirection>1</tp:JourneyDirection>
        <tp:StartEndPlaceJourney>
            <tp:EntranceToPoland>
                <tp:RoutePlace>${routePlaceEntrance}</tp:RoutePlace>
                <tp:RouteNumber>${routeNumberEntrance}</tp:RouteNumber>
            </tp:EntranceToPoland>
            <tp:ExitFromPoland>
                <tp:RoutePlace>Budzisko - Kalwaria</tp:RoutePlace>
                <tp:RouteNumber>DK8</tp:RouteNumber>
            </tp:ExitFromPoland>
            <tp:EndInsidePL>
                <tp:Street>Testowa</tp:Street>
                <tp:HouseNumber>10</tp:HouseNumber>
                <tp:City>Warszawa</tp:City>
                <tp:Country>PL</tp:Country>
                <tp:PostalCode>00-001</tp:PostalCode>
                <tp:CodeTERC>1465011</tp:CodeTERC>
                <tp:Latitude>52.2297</tp:Latitude>
                <tp:Longitude>21.0122</tp:Longitude>
            </tp:EndInsidePL>
        </tp:StartEndPlaceJourney>
    </p:PermissionInfo>

    <p:MeansOfTransport>
        <tp:TruckCountry>UA</tp:TruckCountry>
        <tp:TruckNumber>${data.vehicleInfo.truckNumber}</tp:TruckNumber>
        <tp:TrailerCountry>UA</tp:TrailerCountry>
        <tp:TrailerNumber>${data.vehicleInfo.trailerNumber}</tp:TrailerNumber>
        <tp:GeoLocatorNumber>${data.vehicleInfo.geoLocator}</tp:GeoLocatorNumber>
        <tp:FailoverGeoLocatorNumber>${data.vehicleInfo.geoLocator2}</tp:FailoverGeoLocatorNumber>
        <tp:FailoverCarrierEmail>${CARRIER_EMAIL}</tp:FailoverCarrierEmail>
    </p:MeansOfTransport>

    <p:ResponseAddress>
        <tp:EmailChannel>
            <tp:EmailAddress1><EMAIL></tp:EmailAddress1>
            <tp:EmailAddress2><EMAIL></tp:EmailAddress2>
        </tp:EmailChannel>
        <!-- WebServiceChannel видалено -->
    </p:ResponseAddress>

    <p:Statements>
        <tp:Statement1>true</tp:Statement1>
        <tp:FirstName>Taras</tp:FirstName>
        <tp:LastName>Shkvarok</tp:LastName>
    </p:Statements>
</p:RMPD_100>`;
}

function formatDate_yyyyMMdd(date) {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

function sendRMPDDocumentToTelegram(fileId, chatId) {
    if (!fileId || !chatId) {
        Logger.log('Missing parameters for sending document');
        return false;
    }

    try {
        const documentBlob = DriveApp.getFileById(fileId).getBlob();
        const payload = {
            chat_id: chatId,
            document: documentBlob
        };

        const options = {
            method: 'post',
            payload: payload,
            muteHttpExceptions: true
        };

        const response = UrlFetchApp.fetch(API_URL, options);
        const responseData = JSON.parse(response.getContentText());

        if (responseData.ok) {
            Logger.log('Document sent successfully to Telegram');
            return true;
        } else {
            Logger.log(`Error sending document to Telegram: ${responseData.description}`);
            return false;
        }
    } catch (error) {
        Logger.log('Error sending document: ' + error.toString());
        return false;
    }
}
