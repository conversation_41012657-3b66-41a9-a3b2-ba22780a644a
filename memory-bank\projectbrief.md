# Project Brief

Цей проект стосується створення і ведення документів для розмитнення і повного супроводу імпорту палива в Україну.

**Основні функції та можливості:**
- **Управління паливними резервуарами:** Автоматичний вибір резервуарів для зливу палива з урахуванням їх доступності та типу палива (дизель, бензин).
- **Операції з насосами:** Врахування кількості доступних насосів (наприклад, два паралельні насоси для дизеля) для оптимізації часу зливу.
- **Генерація документів:** Автоматичне створення та заповнення різноманітних технічних та фінансових документів, необхідних для імпорту палива (ВМД, Акти, Договори, Заяви, RMPD100, Заяви на перевезення).
- **Інтеграція з Telegram Bot:** Надсилання документів та повідомлень через Telegram для оперативного інформування.
- **Парсинг даних:** Збір та обробка даних з зовнішніх систем, таких як Orlen, та поштових вкладень (PDF CMR, Invoise, RMPD підтвердження).
- **Інтеграція з Google Sheets:** Використання Google Таблиць як основної бази даних для зберігання та обробки інформації.
- **Інтеграція з BAS/Oracle:** Генерація XML файлів для імпорту даних у системи BAS та взаємодія з Oracle.
- **Моніторинг GPS:** Отримання даних GPS та ETA вантажівок для відстеження логістики.
