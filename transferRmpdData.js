// --- КОНФІГУРАЦІЯ З УНІКАЛЬНИМИ ІМЕНАМИ ---
const rmpdTr_TRANSFER_SHEET_ID = '1IsqVkWE0jGkfzNPiPYktxzIFCnDMF0i3IbgZcglQvt8'; // ID таблиці
const rmpdTr_SOURCE_SHEET_NAME = 'RMPD'; // Аркуш, ЗВІДКИ беремо дані (де зберігаються RMPD підтвердження)
const rmpdTr_TARGET_SHEET_NAME = 'Документи на машини'; // Аркуш, КУДИ записуємо дані

// Назви стовпців в аркуші SOURCE_SHEET_NAME ('RMPD')
const rmpdTr_SOURCE_RMPD_NUM_COL = 'Номер RMPD';
const rmpdTr_SOURCE_VEHICLE_COL = 'Номер автомобіля і причепа';
const rmpdTr_SOURCE_LOADED_COL = 'Завантажений'; // Стовпець для перевірки статусу "ні"
const rmpdTr_SOURCE_ENTRANCE_PLACE_COL = 'Пункт в\'їзду'; // Додано для читання
const rmpdTr_SOURCE_EXIT_PLACE_COL = 'Пункт виїзду';   // Додано для читання

// Назви стовпців в аркуші TARGET_SHEET_NAME ('Документи на машини')
const rmpdTr_TARGET_VEHICLE_COL = 'Номер автомобіля і причепа'; // Стовпець для пошуку
const rmpdTr_TARGET_RMPD_COL = 'RMPD1'; // Стовпець для запису номера RMPD
const rmpdTr_TARGET_RETURN_RMPD_COL = 'RMPD2'; // Стовпець для запису номера зворотньої RMPD
const rmpdTr_TARGET_CMR_NUM_COL = 'Номер CMR'; // Стовпець для читання номера CMR
const rmpdTr_TARGET_CMR_DATE_COL = 'Дата CMR'; // Стовпець для читання дати CMR

// Назва аркуша з реєстром (як у create_RMPD100 актуальна.js)
const rmpdTr_RMPD_REGISTRY_NAME = 'Реєстр водіїв і автомобілів';
// Назви стовпців в реєстрі (для функції rmpdTr_findVehicleInfo)
const rmpdTr_REGISTRY_DRIVER_NAME_COL = 'ПІП Водія';
const rmpdTr_REGISTRY_CAR_NUM_COL = '№ Автомобіля';
const rmpdTr_REGISTRY_TRAILER_NUM_COL = '№ причепа';
const rmpdTr_REGISTRY_GPS1_COL = '№ GPS Трекера';
const rmpdTr_REGISTRY_GPS2_COL = '№ GPS Трекера 2';

// Конфігурація для збереження та відправки зворотньої RMPD
const rmpdTr_RETURN_RMPD_FOLDER_ID = '1WjI5YyynvKQDYe0mHWty3QiDULtkPIIt'; // ID папки для збереження XML
const rmpdTr_BOT_TOKEN_SHIPPING = '7461294369:AAHAsQ-2bVU8hxpeW7heWeAX7UTLa20yaHk'; // Токен Marsik
const rmpdTr_API_URL_SHIPPING = `https://api.telegram.org/bot${rmpdTr_BOT_TOKEN_SHIPPING}/sendDocument`;
// const rmpdTr_CHAT_ID_SHIPPING = '276789696';// чат зі мною
const rmpdTr_CHAT_ID_SHIPPING = '-4631002061'; // chat_id групи

// Статичні дані (з унікальними префіксами)
const rmpdTr_STATIC_CARRIER_INFO = { // Дані з create_RMPD100 актуальна.js
  idSisc: 'FRUA1215779550001',
  traderName: 'MAGISTR TRANS LLC',
  identityType: 'INNY',
  identityNumber: 'UA45561434',
  street: '9 RUE str. Promyslova',
  houseNumber: 'BRAK',
  city: 'Plotycha village',
  country: 'UA',
  postalCode: '47704'
};
const rmpdTr_STATIC_STATEMENTS = { // Дані з create_RMPD100 актуальна.js
  firstName: 'Taras',
  lastName: 'Shkvarok'
};
// Видалено STATIC_MEANS_OF_TRANSPORT, оскільки більшість даних тепер динамічні
const rmpdTr_STATIC_TRUCK_COUNTRY = 'UA'; // Залишаємо статичними, як у create_RMPD100
const rmpdTr_STATIC_TRAILER_COUNTRY = 'UA';
const rmpdTr_STATIC_FAILOVER_EMAIL = '<EMAIL>';
const rmpdTr_STATIC_CARRIER_KEY = ''; // Залишаємо порожнім, щоб система сама згенерувала ключ

// --- КІНЕЦЬ КОНФІГУРАЦІЇ ---

/**
 * Основна функція для перенесення номерів RMPD з аркуша 'RMPD'
 * до аркуша 'Документи на машини' для незавантажених записів.
 * Назва функції змінена для унікальності.
 */
function rmpdTr_transferRmpdNumbers() {
  try {
    Logger.log('--- Запуск rmpdTr_transferRmpdNumbers ---');

    const ss = SpreadsheetApp.openById(rmpdTr_TRANSFER_SHEET_ID);
    const sourceSheet = ss.getSheetByName(rmpdTr_SOURCE_SHEET_NAME);
    const targetSheet = ss.getSheetByName(rmpdTr_TARGET_SHEET_NAME);

    if (!sourceSheet) {
      Logger.log(`Помилка: Аркуш "${rmpdTr_SOURCE_SHEET_NAME}" не знайдено.`);
      return;
    }
    if (!targetSheet) {
      Logger.log(`Помилка: Аркуш "${rmpdTr_TARGET_SHEET_NAME}" не знайдено.`);
      return;
    }

    const sourceData = sourceSheet.getDataRange().getValues();
    const targetData = targetSheet.getDataRange().getValues();

    if (sourceData.length < 2) {
      Logger.log(`Аркуш "${rmpdTr_SOURCE_SHEET_NAME}" порожній або містить тільки заголовок. Перенесення не потрібне.`);
      return;
    }
     if (targetData.length < 2) {
      Logger.log(`Аркуш "${rmpdTr_TARGET_SHEET_NAME}" порожній або містить тільки заголовок. Неможливо знайти відповідності.`);
      return;
    }

    const sourceHeaders = sourceData[0];
    const targetHeaders = targetData[0];

    // Знаходимо індекси стовпців (використовуємо унікальні константи)
    const sourceRmpdNumIdx = sourceHeaders.indexOf(rmpdTr_SOURCE_RMPD_NUM_COL);
    const sourceVehicleIdx = sourceHeaders.indexOf(rmpdTr_SOURCE_VEHICLE_COL);
    const sourceLoadedIdx = sourceHeaders.indexOf(rmpdTr_SOURCE_LOADED_COL);
    const sourceEntrancePlaceIdx = sourceHeaders.indexOf(rmpdTr_SOURCE_ENTRANCE_PLACE_COL); // Індекс пункту в'їзду
    const sourceExitPlaceIdx = sourceHeaders.indexOf(rmpdTr_SOURCE_EXIT_PLACE_COL);       // Індекс пункту виїзду

    const targetVehicleIdx = targetHeaders.indexOf(rmpdTr_TARGET_VEHICLE_COL);
    const targetRmpdColIdx = targetHeaders.indexOf(rmpdTr_TARGET_RMPD_COL);
    const targetCmrNumIdx = targetHeaders.indexOf(rmpdTr_TARGET_CMR_NUM_COL);
    const targetCmrDateIdx = targetHeaders.indexOf(rmpdTr_TARGET_CMR_DATE_COL);

    // Перевірка всіх індексів
    const requiredIndexes = {
        sourceRmpdNum: sourceRmpdNumIdx,
        sourceVehicle: sourceVehicleIdx,
        sourceLoaded: sourceLoadedIdx,
        sourceEntrance: sourceEntrancePlaceIdx,
        sourceExit: sourceExitPlaceIdx,
        targetVehicle: targetVehicleIdx,
        targetRmpd: targetRmpdColIdx,
        targetCmrNum: targetCmrNumIdx,
        targetCmrDate: targetCmrDateIdx
        // Видаляємо перевірку targetDriver, оскільки цей стовпець не потрібен в аркуші "Документи на машини"
        // Інформація про водія береться з реєстру водіїв і автомобілів
    };

    let missingIndex = false;
    for (const key in requiredIndexes) {
        if (requiredIndexes[key] === -1) {
            Logger.log(`Помилка: Не знайдено необхідний стовпець для ключа '${key}' з очікуваною назвою (перевірте константи конфігурації).`);
            missingIndex = true;
        }
    }
    if (missingIndex) {
        Logger.log(`Детальні індекси в "${rmpdTr_SOURCE_SHEET_NAME}": RMPD=${sourceRmpdNumIdx}, Авто=${sourceVehicleIdx}, Завантажений=${sourceLoadedIdx}, В'їзд=${sourceEntrancePlaceIdx}, Виїзд=${sourceExitPlaceIdx}`);
        Logger.log(`Детальні індекси в "${rmpdTr_TARGET_SHEET_NAME}": Авто=${targetVehicleIdx}, RMPD1=${targetRmpdColIdx}, CMR Номер=${targetCmrNumIdx}, CMR Дата=${targetCmrDateIdx}`);

        // Додаткова діагностика
        Logger.log(`Всі заголовки в "${rmpdTr_TARGET_SHEET_NAME}":`);
        targetHeaders.forEach((header, index) => {
            if (header) Logger.log(`Стовпець ${index + 1}: "${header}"`);
        });

        return;
    }

    // Створюємо мапу для швидкого пошуку рядків в цільовому аркуші
    // Ключ: "НомерАвто НомерПричепа", Значення: номер рядка (починаючи з 1)
    const targetVehicleMap = {};
    for (let i = 1; i < targetData.length; i++) {
      const vehicleString = targetData[i][targetVehicleIdx]?.toString().trim();
      if (vehicleString) {
        targetVehicleMap[vehicleString] = i + 1; // Зберігаємо номер рядка в таблиці
      }
    }
    //  Logger.log(`Створено мапу пошуку для ${Object.keys(targetVehicleMap).length} унікальних транспортних засобів в "${rmpdTr_TARGET_SHEET_NAME}".`);

    let transferredCount = 0;
    let rowsToDelete = []; // Масив для зберігання номерів рядків, які потрібно видалити

    // Ітеруємо по аркушу "RMPD" ЗНИЗУ ВГОРУ
    for (let i = sourceData.length - 1; i >= 1; i--) {
      const sourceRow = sourceData[i];
      const isLoaded = sourceRow[sourceLoadedIdx]?.toString().toLowerCase();
      const vehicleString = sourceRow[sourceVehicleIdx]?.toString().trim();
      const rmpdNumber = sourceRow[sourceRmpdNumIdx]?.toString().trim();
      const entrancePlace = sourceRow[sourceEntrancePlaceIdx]?.toString().trim(); // Пункт в'їзду з RMPD
      const exitPlace = sourceRow[sourceExitPlaceIdx]?.toString().trim();       // Пункт виїзду з RMPD

      // Перевіряємо, чи статус "ні" і чи є всі дані для пошуку та генерації
      if (isLoaded === 'ні' && vehicleString && rmpdNumber && entrancePlace && exitPlace) {
        // Шукаємо відповідний рядок в цільовому аркуші
        const targetRowIndex = targetVehicleMap[vehicleString];

        if (targetRowIndex) {
          // Знайдено відповідний рядок в "Документи на машини"
          const targetRowData = targetData[targetRowIndex - 1]; // -1 бо індекси масиву з 0
          const cmrNumber = targetRowData[targetCmrNumIdx]?.toString().trim();
          const cmrDate = targetRowData[targetCmrDateIdx];

          if (!cmrNumber || !cmrDate) {
            Logger.log(`В рядку ${targetRowIndex} аркуша "${rmpdTr_TARGET_SHEET_NAME}" відсутній номер/дата CMR для авто "${vehicleString}". Пропуск генерації зворотньої RMPD.`);
            continue; // Переходимо до наступного рядка в "RMPD"
          }

          // 1. Записуємо номер RMPD в цільовий аркуш
          let rmpdWriteSuccess = false;
          const currentRmpd1Value = targetSheet.getRange(targetRowIndex, targetRmpdColIdx + 1).getValue();
          if (currentRmpd1Value !== '' && currentRmpd1Value !== null) {
            Logger.log(`Комірка RMPD1 в рядку ${targetRowIndex} аркуша "${rmpdTr_TARGET_SHEET_NAME}" для авто "${vehicleString}" вже містить значення "${currentRmpd1Value}". Перезапис RMPD "${rmpdNumber}" пропущено.`);
            continue; // Переходимо до наступного рядка в "RMPD", не видаляючи поточний
          }

          try {
            targetSheet.getRange(targetRowIndex, targetRmpdColIdx + 1).setValue(rmpdNumber);
            Logger.log(`Номер RMPD "${rmpdNumber}" перенесено до рядка ${targetRowIndex} аркуша "${rmpdTr_TARGET_SHEET_NAME}" для авто "${vehicleString}".`);
            rmpdWriteSuccess = true;
          } catch (e) {
             Logger.log(`Помилка запису RMPD "${rmpdNumber}" у рядок ${targetRowIndex}: ${e}`);
             continue; // Не можемо продовжити без запису RMPD
          }

          // 2. Генеруємо та відправляємо XML зворотньої RMPD
          if (rmpdWriteSuccess) {
             // Знаходимо інформацію про транспорт за номером автомобіля (використовуємо унікальну функцію)
             const vehicleInfo = rmpdTr_findVehicleInfoByNumber(vehicleString);
             if (!vehicleInfo) {
                Logger.log(`Не вдалося знайти інформацію про транспорт для авто "${vehicleString}" в реєстрі. Пропуск генерації зворотньої RMPD.`);
                continue; // Переходимо до наступного рядка в "RMPD"
             }

             const returnRmpdData = {
               originalRmpdNumber: rmpdNumber,
               cmrNumber: cmrNumber,
               vehicleNumberString: vehicleString,
               entrancePlace: entrancePlace,
               exitPlace: exitPlace,
               cmrDate: cmrDate,
               vehicleInfo: vehicleInfo // Передаємо дані про транспорт
             };

             // Використовуємо унікальну функцію
             const xmlGenerationSuccess = rmpdTr_generateAndSendReturnRmpdXml(returnRmpdData);

             // 3. Додаємо рядок для видалення тільки якщо все успішно
             if (xmlGenerationSuccess) {
               rowsToDelete.push(i + 1); // Зберігаємо номер рядка в таблиці
               transferredCount++;
             } else {
                Logger.log(`Не вдалося згенерувати/відправити зворотній RMPD для ${rmpdNumber}. Рядок не буде видалено з "${rmpdTr_SOURCE_SHEET_NAME}".`);
             }
          }
        } else {
          //  Logger.log(`Не знайдено відповідного запису в "${rmpdTr_TARGET_SHEET_NAME}" для авто "${vehicleString}" з рядка ${i + 1} аркуша "${rmpdTr_SOURCE_SHEET_NAME}".`);
        }
      } else {
         // Можна розкоментувати для детального логування пропусків
         // if (isLoaded !== 'ні') Logger.log(`Пропуск рядка ${i + 1} ("${rmpdTr_SOURCE_SHEET_NAME}"): Статус не 'ні' (${isLoaded})`);
         // else if (!vehicleString) Logger.log(`Пропуск рядка ${i + 1} ("${rmpdTr_SOURCE_SHEET_NAME}"): Відсутній номер авто/причепа`);
         // else if (!rmpdNumber) Logger.log(`Пропуск рядка ${i + 1} ("${rmpdTr_SOURCE_SHEET_NAME}"): Відсутній номер RMPD`);
         // else if (!entrancePlace) Logger.log(`Пропуск рядка ${i + 1} ("${rmpdTr_SOURCE_SHEET_NAME}"): Відсутній пункт в'їзду`);
         // else if (!exitPlace) Logger.log(`Пропуск рядка ${i + 1} ("${rmpdTr_SOURCE_SHEET_NAME}"): Відсутній пункт виїзду`);
      }
    }

    // Видаляємо оброблені рядки з аркуша "RMPD"
    if (rowsToDelete.length > 0) {
      rowsToDelete.sort((a, b) => b - a); // Сортуємо у зворотному порядку!
      Logger.log(`Підготовка до видалення ${rowsToDelete.length} рядків з аркуша "${rmpdTr_SOURCE_SHEET_NAME}": ${JSON.stringify(rowsToDelete)}`);
      let deletedCount = 0;
      for (const rowIndexToDelete of rowsToDelete) {
        try {
            sourceSheet.deleteRow(rowIndexToDelete);
            deletedCount++;
            Utilities.sleep(100); // Невелика пауза між видаленнями
        } catch (e) {
            Logger.log(`Помилка видалення рядка ${rowIndexToDelete} з "${rmpdTr_SOURCE_SHEET_NAME}": ${e}`);
            // Можна додати логіку повторної спроби або просто продовжити
        }
      }
       Logger.log(`Успішно видалено ${deletedCount} з ${rowsToDelete.length} запланованих рядків.`);
    } else {
        Logger.log(`Немає рядків для видалення з аркуша "${rmpdTr_SOURCE_SHEET_NAME}".`);
    }

    Logger.log(`--- rmpdTr_transferRmpdNumbers завершено. Перенесено ${transferredCount} записів. ---`);

    // Викликаємо функцію для обробки зворотніх RMPD
    rmpdTr_transferReturnRmpdNumbers();

  } catch (error) {
    Logger.log('Критична помилка в rmpdTr_transferRmpdNumbers: ' + error.toString() + "\n" + error.stack);
  }
}


/**
 * Функція для перенесення номерів зворотніх RMPD з аркуша 'RMPD'
 * до аркуша 'Документи на машини' для завантажених записів (статус "так").
 * Записує номер RMPD в стовпець "RMPD2".
 */
function rmpdTr_transferReturnRmpdNumbers() {
  try {

    const ss = SpreadsheetApp.openById(rmpdTr_TRANSFER_SHEET_ID);
    const sourceSheet = ss.getSheetByName(rmpdTr_SOURCE_SHEET_NAME);
    const targetSheet = ss.getSheetByName(rmpdTr_TARGET_SHEET_NAME);

    if (!sourceSheet) {
      Logger.log(`Помилка: Аркуш "${rmpdTr_SOURCE_SHEET_NAME}" не знайдено.`);
      return;
    }
    if (!targetSheet) {
      Logger.log(`Помилка: Аркуш "${rmpdTr_TARGET_SHEET_NAME}" не знайдено.`);
      return;
    }

    const sourceData = sourceSheet.getDataRange().getValues();
    const targetData = targetSheet.getDataRange().getValues();

    if (sourceData.length < 2) {
      Logger.log(`Аркуш "${rmpdTr_SOURCE_SHEET_NAME}" порожній або містить тільки заголовок. Перенесення не потрібне.`);
      return;
    }
    if (targetData.length < 2) {
      Logger.log(`Аркуш "${rmpdTr_TARGET_SHEET_NAME}" порожній або містить тільки заголовок. Неможливо знайти відповідності.`);
      return;
    }

    const sourceHeaders = sourceData[0];
    const targetHeaders = targetData[0];

    // Знаходимо індекси стовпців
    const sourceRmpdNumIdx = sourceHeaders.indexOf(rmpdTr_SOURCE_RMPD_NUM_COL);
    const sourceVehicleIdx = sourceHeaders.indexOf(rmpdTr_SOURCE_VEHICLE_COL);
    const sourceLoadedIdx = sourceHeaders.indexOf(rmpdTr_SOURCE_LOADED_COL);

    const targetVehicleIdx = targetHeaders.indexOf(rmpdTr_TARGET_VEHICLE_COL);
    const targetReturnRmpdColIdx = targetHeaders.indexOf(rmpdTr_TARGET_RETURN_RMPD_COL);

    // Перевірка всіх індексів
    const requiredIndexes = {
        sourceRmpdNum: sourceRmpdNumIdx,
        sourceVehicle: sourceVehicleIdx,
        sourceLoaded: sourceLoadedIdx,
        targetVehicle: targetVehicleIdx,
        targetReturnRmpd: targetReturnRmpdColIdx
    };

    let missingIndex = false;
    for (const key in requiredIndexes) {
        if (requiredIndexes[key] === -1) {
            Logger.log(`Помилка: Не знайдено необхідний стовпець для ключа '${key}' з очікуваною назвою (перевірте константи конфігурації).`);
            missingIndex = true;
        }
    }
    if (missingIndex) {
        Logger.log(`Детальні індекси в "${rmpdTr_SOURCE_SHEET_NAME}": RMPD=${sourceRmpdNumIdx}, Авто=${sourceVehicleIdx}, Завантажений=${sourceLoadedIdx}`);
        Logger.log(`Детальні індекси в "${rmpdTr_TARGET_SHEET_NAME}": Авто=${targetVehicleIdx}, RMPD2=${targetReturnRmpdColIdx}`);
        return;
    }

    // Створюємо мапу для швидкого пошуку рядків в цільовому аркуші
    // Ключ: "НомерАвто НомерПричепа", Значення: номер рядка (починаючи з 1)
    const targetVehicleMap = {};
    for (let i = 1; i < targetData.length; i++) {
      const vehicleString = targetData[i][targetVehicleIdx]?.toString().trim();
      if (vehicleString) {
        targetVehicleMap[vehicleString] = i + 1; // Зберігаємо номер рядка в таблиці
      }
    }
    // Logger.log(`Створено мапу пошуку для ${Object.keys(targetVehicleMap).length} унікальних транспортних засобів в "${rmpdTr_TARGET_SHEET_NAME}".`);

    let transferredCount = 0;
    let rowsToDelete = []; // Масив для зберігання номерів рядків, які потрібно видалити

    // Ітеруємо по аркушу "RMPD" ЗНИЗУ ВГОРУ
    for (let i = sourceData.length - 1; i >= 1; i--) {
      const sourceRow = sourceData[i];
      const isLoaded = sourceRow[sourceLoadedIdx]?.toString().toLowerCase();
      const vehicleString = sourceRow[sourceVehicleIdx]?.toString().trim();
      const rmpdNumber = sourceRow[sourceRmpdNumIdx]?.toString().trim();

      // Перевіряємо, чи статус "так" і чи є всі дані для пошуку
      if (isLoaded === 'так' && vehicleString && rmpdNumber) {
        // Шукаємо відповідний рядок в цільовому аркуші
        const targetRowIndex = targetVehicleMap[vehicleString];

        if (targetRowIndex) {
          // Знайдено відповідний рядок в "Документи на машини"
          // закоментовано щоб не перевіряти RMPD2 на перезапис так як може бути в RMPD зміни
          // const currentRmpd2Value = targetSheet.getRange(targetRowIndex, targetReturnRmpdColIdx + 1).getValue();
          // if (currentRmpd2Value !== '' && currentRmpd2Value !== null) {
          //   Logger.log(`Комірка RMPD2 в рядку ${targetRowIndex} аркуша "${rmpdTr_TARGET_SHEET_NAME}" для авто "${vehicleString}" вже містить значення "${currentRmpd2Value}". Перезапис зворотньої RMPD "${rmpdNumber}" пропущено.`);
          //   continue; // Переходимо до наступного рядка в "RMPD", не видаляючи поточний
          // }

          try {
            // Записуємо номер зворотньої RMPD в стовпець "RMPD2"
            targetSheet.getRange(targetRowIndex, targetReturnRmpdColIdx + 1).setValue(rmpdNumber);
            Logger.log(`Номер зворотньої RMPD "${rmpdNumber}" перенесено до рядка ${targetRowIndex} аркуша "${rmpdTr_TARGET_SHEET_NAME}" (стовпець RMPD2) для авто "${vehicleString}".`);

            // Додаємо рядок для видалення
            rowsToDelete.push(i + 1); // Зберігаємо номер рядка в таблиці
            transferredCount++;
          } catch (e) {
            Logger.log(`Помилка запису зворотньої RMPD "${rmpdNumber}" у рядок ${targetRowIndex}: ${e}`);
          }
        } else {
          Logger.log(`Не знайдено відповідного запису в "${rmpdTr_TARGET_SHEET_NAME}" для авто "${vehicleString}" з рядка ${i + 1} аркуша "${rmpdTr_SOURCE_SHEET_NAME}".`);
        }
      }
    }

    // Видаляємо оброблені рядки з аркуша "RMPD"
    if (rowsToDelete.length > 0) {
      rowsToDelete.sort((a, b) => b - a); // Сортуємо у зворотному порядку!
      Logger.log(`Підготовка до видалення ${rowsToDelete.length} рядків з аркуша "${rmpdTr_SOURCE_SHEET_NAME}": ${JSON.stringify(rowsToDelete)}`);
      let deletedCount = 0;
      for (const rowIndexToDelete of rowsToDelete) {
        try {
            sourceSheet.deleteRow(rowIndexToDelete);
            deletedCount++;
            Utilities.sleep(100); // Невелика пауза між видаленнями
        } catch (e) {
            Logger.log(`Помилка видалення рядка ${rowIndexToDelete} з "${rmpdTr_SOURCE_SHEET_NAME}": ${e}`);
        }
      }
      Logger.log(`Успішно видалено ${deletedCount} з ${rowsToDelete.length} запланованих рядків.`);
    } else {
      Logger.log(`Немає рядків для видалення з аркуша "${rmpdTr_SOURCE_SHEET_NAME}".`);
    }

    Logger.log(`--- rmpdTr_transferReturnRmpdNumbers завершено. Перенесено ${transferredCount} записів. ---`);

  } catch (error) {
    Logger.log('Критична помилка в rmpdTr_transferReturnRmpdNumbers: ' + error.toString() + "\n" + error.stack);
  }
}

/**
 * Функція пошуку даних про транспорт в реєстрі за номером автомобіля.
 * @param {string} vehicleString Номер автомобіля і причепа для пошуку (у форматі "НомерАвто НомерПричепа").
 * @return {object|null} Об'єкт з даними про транспорт або null, якщо не знайдено.
 */
function rmpdTr_findVehicleInfoByNumber(vehicleString) {
    // Використовуємо той самий ID таблиці, що й для інших аркушів
    const sheet = SpreadsheetApp.openById(rmpdTr_TRANSFER_SHEET_ID).getSheetByName(rmpdTr_RMPD_REGISTRY_NAME);
    if (!sheet) {
      Logger.log(`Помилка: Аркуш реєстру "${rmpdTr_RMPD_REGISTRY_NAME}" не знайдено.`);
      return null;
    }

    const data = sheet.getDataRange().getValues();
    if (data.length < 2) {
        Logger.log(`Аркуш реєстру "${rmpdTr_RMPD_REGISTRY_NAME}" порожній або містить тільки заголовок.`);
        return null;
    }

    const headers = data[0];

    // Використовуємо унікальні константи для назв стовпців
    const driverIndex = headers.indexOf(rmpdTr_REGISTRY_DRIVER_NAME_COL);
    const carNumberIndex = headers.indexOf(rmpdTr_REGISTRY_CAR_NUM_COL);
    const trailerNumberIndex = headers.indexOf(rmpdTr_REGISTRY_TRAILER_NUM_COL);
    const gpsTrackerIndex = headers.indexOf(rmpdTr_REGISTRY_GPS1_COL);
    const gpsTracker2Index = headers.indexOf(rmpdTr_REGISTRY_GPS2_COL);

    // Перевірка індексів
    if ([driverIndex, carNumberIndex, trailerNumberIndex, gpsTrackerIndex, gpsTracker2Index].includes(-1)) {
       Logger.log('Помилка: Не знайдено один або декілька стовпців у реєстрі водіїв згідно наданих назв (перевірте константи rmpdTr_REGISTRY_...).');
       Logger.log(`Індекси: ПІП Водія=${driverIndex}, № Автомобіля=${carNumberIndex}, № причепа=${trailerNumberIndex}, № GPS Трекера=${gpsTrackerIndex}, № GPS Трекера 2=${gpsTracker2Index}`);
       return null;
    }

    // Розділяємо рядок на номер авто та причепа
    const vehicleParts = vehicleString.split(' ');
    const truckNumber = vehicleParts[0] || '';
    const trailerNumber = vehicleParts[1] || '';

    // Шукаємо в реєстрі за номером автомобіля
    for (let i = 1; i < data.length; i++) {
        const carNum = data[i][carNumberIndex]?.toString().trim();
        const trailerNum = data[i][trailerNumberIndex]?.toString().trim();

        // Перевіряємо чи збігається номер автомобіля
        if (carNum === truckNumber) {
            // Якщо є причіп, перевіряємо його також
            if (trailerNumber && trailerNum !== trailerNumber) {
                continue; // Причіп не збігається, продовжуємо пошук
            }

            Logger.log(`Знайдено дані для авто "${vehicleString}" в реєстрі (рядок ${i + 1}).`);
            return {
                driverName: data[i][driverIndex]?.toString().trim(),
                truckNumber: carNum,
                trailerNumber: trailerNum,
                geoLocator: data[i][gpsTrackerIndex]?.toString().trim() || '',  // Primary GPS tracker
                geoLocator2: data[i][gpsTracker2Index]?.toString().trim() || '' // Secondary GPS tracker
            };
        }
    }

    Logger.log(`Попередження: Не знайдено даних для авто "${vehicleString}" в реєстрі "${rmpdTr_RMPD_REGISTRY_NAME}".`);
    return null; // Повертаємо null, якщо автомобіль не знайдено
}

/**
 * Функція пошуку даних про транспорт в реєстрі.
 * Назва функції змінена для унікальності.
 * Використовує унікальні константи для назв стовпців реєстру.
 * @param {string} driverName Ім'я водія для пошуку.
 * @return {object|null} Об'єкт з даними про транспорт або null, якщо не знайдено.
 */
function rmpdTr_findVehicleInfo(driverName) {
    // Використовуємо той самий ID таблиці, що й для інших аркушів
    const sheet = SpreadsheetApp.openById(rmpdTr_TRANSFER_SHEET_ID).getSheetByName(rmpdTr_RMPD_REGISTRY_NAME);
    if (!sheet) {
      Logger.log(`Помилка: Аркуш реєстру "${rmpdTr_RMPD_REGISTRY_NAME}" не знайдено.`);
      return null;
    }
    const data = sheet.getDataRange().getValues();
    if (data.length < 2) {
        Logger.log(`Аркуш реєстру "${rmpdTr_RMPD_REGISTRY_NAME}" порожній або містить тільки заголовок.`);
        return null;
    }
    const headers = data[0];

    // Використовуємо унікальні константи для назв стовпців
    const driverIndex = headers.indexOf(rmpdTr_REGISTRY_DRIVER_NAME_COL);
    const carNumberIndex = headers.indexOf(rmpdTr_REGISTRY_CAR_NUM_COL);
    const trailerNumberIndex = headers.indexOf(rmpdTr_REGISTRY_TRAILER_NUM_COL);
    const gpsTrackerIndex = headers.indexOf(rmpdTr_REGISTRY_GPS1_COL);
    const gpsTracker2Index = headers.indexOf(rmpdTr_REGISTRY_GPS2_COL);

     // Перевірка індексів
    if ([driverIndex, carNumberIndex, trailerNumberIndex, gpsTrackerIndex, gpsTracker2Index].includes(-1)) {
       Logger.log('Помилка: Не знайдено один або декілька стовпців у реєстрі водіїв згідно наданих назв (перевірте константи rmpdTr_REGISTRY_...).');
       Logger.log(`Індекси: ПІП Водія=${driverIndex}, № Автомобіля=${carNumberIndex}, № причепа=${trailerNumberIndex}, № GPS Трекера=${gpsTrackerIndex}, № GPS Трекера 2=${gpsTracker2Index}`);
       return null;
    }

    for (let i = 1; i < data.length; i++) {
        // Порівнюємо імена, видаливши зайві пробіли
        if (data[i][driverIndex]?.toString().trim() === driverName?.toString().trim()) {
             Logger.log(`Знайдено дані для водія "${driverName}" в реєстрі (рядок ${i + 1}).`);
            return {
                truckNumber: data[i][carNumberIndex]?.toString().trim(),
                trailerNumber: data[i][trailerNumberIndex]?.toString().trim(),
                geoLocator: data[i][gpsTrackerIndex]?.toString().trim() || '',  // Primary GPS tracker
                geoLocator2: data[i][gpsTracker2Index]?.toString().trim() || '' // Secondary GPS tracker
            };
        }
    }
    Logger.log(`Попередження: Не знайдено даних для водія "${driverName}" в реєстрі "${rmpdTr_RMPD_REGISTRY_NAME}".`);
    return null; // Повертаємо null, якщо водія не знайдено
}


/**
 * Генерує XML для зворотньої RMPD (RMPD_101), зберігає його та відправляє в Telegram.
 * Назва функції змінена для унікальності.
 * Використовує унікальні константи та функції.
 * @param {object} data Об'єкт з даними для генерації XML.
 * @return {boolean} true, якщо все пройшло успішно, інакше false.
 */
function rmpdTr_generateAndSendReturnRmpdXml(data) {
  try {
    Logger.log(`Генерація зворотнього RMPD для ${data.originalRmpdNumber}`);

    // Розділяємо номер авто та причепа
    const vehicleParts = data.vehicleNumberString.split(' ');
    const truckNumber = vehicleParts[0] || '';
    const trailerNumber = vehicleParts[1] || '';

    // Перевіряємо, чи отримали дані з реєстру
    if (!data.vehicleInfo) {
       Logger.log(`Помилка: Не вдалося отримати vehicleInfo для генерації XML для ${data.originalRmpdNumber}`);
       return false; // Не можемо генерувати без даних про транспорт
    }
    // Отримуємо актуальні дані про GPS ВИКЛЮЧНО з vehicleInfo
    const geoLocator = data.vehicleInfo.geoLocator;
    const failoverGeoLocator = data.vehicleInfo.geoLocator2;

     // Перевірка наявності основного GPS трекера (опціонально, можна розкоментувати для обов'язковості)
    // if (!geoLocator) {
    //    Logger.log(`Помилка: Відсутній основний GPS трекер (geoLocator) для ${data.originalRmpdNumber} у даних vehicleInfo.`);
    //    return false;
    // }


    // Визначаємо нові пункти в'їзду/виїзду та їх номери доріг (використовуємо унікальну функцію)
    const newEntrancePlace = data.exitPlace; // Оригінальний виїзд стає новим в'їздом
    const newExitPlace = data.entrancePlace; // Оригінальний в'їзд стає новим виїздом
    const newEntranceRouteNum = rmpdTr_getRouteNumber(newEntrancePlace);
    const newExitRouteNum = rmpdTr_getRouteNumber(newExitPlace);

    // Розраховуємо дати (використовуємо унікальні функції)
    const startDate = new Date(data.cmrDate);
    const endDate = rmpdTr_addDays(startDate, 5);
    const formattedStartDate = rmpdTr_formatDateForXml(startDate);
    const formattedEndDate = rmpdTr_formatDateForXml(endDate);

    if (!formattedStartDate || !formattedEndDate) {
        Logger.log(`Помилка форматування дат для RMPD ${data.originalRmpdNumber}. Початкова дата CMR: ${data.cmrDate}`);
        return false;
    }

    // Формуємо XML контент (схема RMPD_100, використовуємо унікальні константи)
    const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<p:RMPD_100 xmlns:p="http://www.mf.gov.pl/RMPD/2024/03/18/RMPD_100.xsd"
            xmlns:tp="http://www.mf.gov.pl/RMPD/2024/03/18/PTypes.xsd">
    <!-- Базова інформація про перевізника -->
    <p:Carrier>
        <tp:TraderInfo>
            <tp:IdSisc>${rmpdTr_STATIC_CARRIER_INFO.idSisc}</tp:IdSisc>
            <tp:TraderName>${rmpdTr_STATIC_CARRIER_INFO.traderName}</tp:TraderName>
            <tp:TraderIdentityType>${rmpdTr_STATIC_CARRIER_INFO.identityType}</tp:TraderIdentityType>
            <tp:TraderIdentityNumber>${rmpdTr_STATIC_CARRIER_INFO.identityNumber}</tp:TraderIdentityNumber>
        </tp:TraderInfo>
        <tp:TraderAddress>
            <tp:Street>${rmpdTr_STATIC_CARRIER_INFO.street}</tp:Street>
            <tp:HouseNumber>${rmpdTr_STATIC_CARRIER_INFO.houseNumber}</tp:HouseNumber>
            <tp:City>${rmpdTr_STATIC_CARRIER_INFO.city}</tp:City>
            <tp:Country>${rmpdTr_STATIC_CARRIER_INFO.country}</tp:Country>
            <tp:PostalCode>${rmpdTr_STATIC_CARRIER_INFO.postalCode}</tp:PostalCode>
        </tp:TraderAddress>
    </p:Carrier>

    <!-- Інформація про відправника для зворотнього рейсу -->
    <p:GoodsSender>
        <tp:TraderInfo>
            <tp:TraderName>PUBLIC COMPANY ORLEN LIETUVA</tp:TraderName>
            <tp:TraderIdentityType>INNY</tp:TraderIdentityType>
            <tp:TraderIdentityNumber>*********</tp:TraderIdentityNumber>
        </tp:TraderInfo>
        <tp:TraderAddress>
            <tp:Street>MAZEIKIU</tp:Street>
            <tp:HouseNumber>75</tp:HouseNumber>
            <tp:City>JUODEIKIU</tp:City>
            <tp:Country>LT</tp:Country>
            <tp:PostalCode>LT-89453</tp:PostalCode>
        </tp:TraderAddress>
    </p:GoodsSender>

    <!-- Інформація про одержувача для зворотнього рейсу -->
    <p:GoodsRecipient>
        <tp:TraderInfo>
            <tp:TraderName>MAGISTR-D LLC</tp:TraderName>
            <tp:TraderIdentityType>INNY</tp:TraderIdentityType>
            <tp:TraderIdentityNumber>UA21141124</tp:TraderIdentityNumber>
        </tp:TraderInfo>
        <tp:TraderAddress>
            <tp:Street>Kn.Ostrozkogo</tp:Street>
            <tp:HouseNumber>47a</tp:HouseNumber>
            <tp:City>Ternopil</tp:City>
            <tp:Country>UA</tp:Country>
            <tp:PostalCode>46006</tp:PostalCode>
        </tp:TraderAddress>
    </p:GoodsRecipient>

    <p:PermissionInfo>
        <tp:StartTransportDate>${formattedStartDate}</tp:StartTransportDate>
        <tp:EndTransportDate>${formattedEndDate}</tp:EndTransportDate>
        <tp:CountryLoadCode>LT</tp:CountryLoadCode>
        <tp:CountryUnloadCode>UA</tp:CountryUnloadCode>
        <tp:Loaded>true</tp:Loaded>
        <tp:TransportDocuments>
            <tp:TypeOfTransportDocument>CMR</tp:TypeOfTransportDocument>
            <tp:NumberOfTransportDocument>${data.cmrNumber}</tp:NumberOfTransportDocument>
        </tp:TransportDocuments>
        <tp:TypeOfRoadTransport>TRANSIT_TRANSPORT</tp:TypeOfRoadTransport>
        <tp:TypeOfPermission>NOT_OBLIGED</tp:TypeOfPermission>
        <tp:NotObligedLegalBase>094</tp:NotObligedLegalBase>
        <tp:PreviousRmpdNumber>${data.originalRmpdNumber}</tp:PreviousRmpdNumber>
        <tp:JourneyDirection>2</tp:JourneyDirection>
        <tp:StartEndPlaceJourney>
            <tp:EntranceToPoland>
                <tp:RoutePlace>${newEntrancePlace}</tp:RoutePlace>
                <tp:RouteNumber>${newEntranceRouteNum}</tp:RouteNumber>
            </tp:EntranceToPoland>
            <tp:ExitFromPoland>
                <tp:RoutePlace>${newExitPlace}</tp:RoutePlace>
                <tp:RouteNumber>${newExitRouteNum}</tp:RouteNumber>
            </tp:ExitFromPoland>
        </tp:StartEndPlaceJourney>
    </p:PermissionInfo>

    <p:MeansOfTransport>
        <tp:TruckCountry>${rmpdTr_STATIC_TRUCK_COUNTRY}</tp:TruckCountry>
        <tp:TruckNumber>${truckNumber}</tp:TruckNumber>
        <tp:TrailerCountry>${rmpdTr_STATIC_TRAILER_COUNTRY}</tp:TrailerCountry>
        <tp:TrailerNumber>${trailerNumber}</tp:TrailerNumber>
        <tp:GeoLocatorNumber>${geoLocator || ''}</tp:GeoLocatorNumber>
        <tp:FailoverGeoLocatorNumber>${failoverGeoLocator || ''}</tp:FailoverGeoLocatorNumber>
        <tp:FailoverCarrierEmail>${rmpdTr_STATIC_FAILOVER_EMAIL}</tp:FailoverCarrierEmail>
    </p:MeansOfTransport>

    <p:ResponseAddress>
        <tp:EmailChannel>
            <tp:EmailAddress1><EMAIL></tp:EmailAddress1>
            <tp:EmailAddress2><EMAIL></tp:EmailAddress2>
        </tp:EmailChannel>
        <tp:WebServiceChannel>
            <tp:WsFromSISC>true</tp:WsFromSISC>
        </tp:WebServiceChannel>
    </p:ResponseAddress>

    <p:Statements>
        <tp:Statement1>true</tp:Statement1>
        <tp:FirstName>${rmpdTr_STATIC_STATEMENTS.firstName}</tp:FirstName>
        <tp:LastName>${rmpdTr_STATIC_STATEMENTS.lastName}</tp:LastName>
    </p:Statements>
</p:RMPD_100>`;

    // Зберігаємо XML файл
    const fileName = `RMPD100_RETURN_${data.originalRmpdNumber}_${Utilities.formatDate(new Date(), Session.getScriptTimeZone(), "yyyyMMddHHmmss")}.xml`;
    const folder = DriveApp.getFolderById(rmpdTr_RETURN_RMPD_FOLDER_ID);
    // Використовуємо явний MIME-тип замість застарілого MimeType.XML
    const file = folder.createFile(fileName, xmlContent, "application/xml");
    Logger.log(`Зворотній RMPD XML файл "${fileName}" збережено в папку ID ${rmpdTr_RETURN_RMPD_FOLDER_ID}`);

    // Відправляємо в Telegram (використовуємо унікальну функцію)
    // Передаємо дані про автомобіль та водія
    const driverName = data.vehicleInfo.driverName || '';
    rmpdTr_sendReturnRmpdToTelegram(file.getId(), fileName, truckNumber, trailerNumber, driverName);

    return true; // Успіх

  } catch (error) {
    Logger.log(`Помилка генерації/відправки зворотнього RMPD для ${data.originalRmpdNumber}: ${error.toString()}\n${error.stack}`);
    return false; // Помилка
  }
}

/**
 * Відправляє згенерований XML файл зворотньої RMPD в Telegram.
 * Назва функції змінена для унікальності.
 * Використовує унікальні константи.
 * @param {string} fileId ID файлу на Google Drive.
 * @param {string} fileName Назва файлу.
 * @param {string} truckNumber Номер автомобіля.
 * @param {string} trailerNumber Номер причепа.
 * @param {string} driverName ПІП водія.
 */
function rmpdTr_sendReturnRmpdToTelegram(fileId, fileName, truckNumber, trailerNumber, driverName) {
  try {
    const file = DriveApp.getFileById(fileId);
    if (!file) {
        Logger.log(`Помилка: Файл з ID ${fileId} не знайдено на Google Drive.`);
        return;
    }
    const documentBlob = file.getBlob();
    // Перейменовуємо блоб, щоб ім'я файлу в Telegram було коректним
    documentBlob.setName(fileName);

    // Перевірка на наявність даних для підпису
    const safeDriverName = driverName || 'Не вказано';
    const safeTruckNumber = truckNumber || 'Не вказано';
    const safeTrailerNumber = trailerNumber || 'Не вказано';

    const payload = {
      chat_id: rmpdTr_CHAT_ID_SHIPPING,
      document: documentBlob,
      // додаємо номер автомобіля та причепа в підпис піп водія
      caption: `Згенеровано зворотній RMPD: \n Номер автомобіля: ${safeTruckNumber}, \n Номер причепа: ${safeTrailerNumber}, \n ПІП водія: ${safeDriverName}` // Додаємо підпис
    };

    const options = {
        method: 'post',
        // 'contentType': 'multipart/form-data', // UrlFetchApp додає це автоматично для блобів
        payload: payload,
        muteHttpExceptions: true // Дозволяє отримати відповідь навіть при помилці
    };

    const response = UrlFetchApp.fetch(rmpdTr_API_URL_SHIPPING, options);
    const responseCode = response.getResponseCode();
    const responseBody = response.getContentText();

    if (responseCode === 200) {
       Logger.log(`Файл "${fileName}" успішно відправлено в Telegram чат ID ${rmpdTr_CHAT_ID_SHIPPING}`);
    } else {
       Logger.log(`Помилка відправки файлу "${fileName}" в Telegram. Код: ${responseCode}, Відповідь: ${responseBody}`);
       // Додаткова інформація для діагностики
       Logger.log(`Payload keys sent: ${Object.keys(payload).join(', ')}`);
       Logger.log(`File size: ${documentBlob.getBytes().length} bytes`);
       Logger.log(`API URL: ${rmpdTr_API_URL_SHIPPING}`);
    }
  } catch (error) {
     Logger.log(`Критична помилка відправки файлу "${fileName}" в Telegram: ${error.toString()}\n${error.stack}`);
  }
}

/**
 * Допоміжна функція для отримання номера дороги за назвою пункту перетину.
 * Назва функції змінена для унікальності.
 * @param {string} placeName Назва пункту перетину.
 * @return {string} Номер дороги або порожній рядок.
 */
function rmpdTr_getRouteNumber(placeName) {
  if (!placeName) return '';
  const lowerPlaceName = placeName.toLowerCase().trim();
  // Більш точні перевірки
  if (lowerPlaceName.includes('dorohusk') || lowerPlaceName.includes('дорогуськ')) return 'DK12';
  if (lowerPlaceName.includes('hrebenne') || lowerPlaceName.includes('гребенне')) return 'DK17';
  if (lowerPlaceName.includes('zosin') || lowerPlaceName.includes('зосин')) return 'DW816'; // Часто використовують номер дороги, а не S74 для Zosin
  if (lowerPlaceName.includes('budzisko') || lowerPlaceName.includes('будзіско')) return 'S61'; // Або DK8, залежно від точки
  if (lowerPlaceName.includes('medyka') || lowerPlaceName.includes('медика')) return 'DK28';
  if (lowerPlaceName.includes('korczowa') || lowerPlaceName.includes('корчова')) return 'DK94'; // Або A4, залежно від точки
  if (lowerPlaceName.includes('barwinek') || lowerPlaceName.includes('барвінок')) return 'DK19'; // Або A4, залежно від точки
  // Додайте інші пункти за потреби
  Logger.log(`Не вдалося визначити номер дороги для пункту: "${placeName}". Повернення порожнього рядка.`);
  return ''; // Повертаємо порожній рядок, якщо не знайдено
}

/**
 * Допоміжна функція для додавання днів до дати.
 * Назва функції змінена для унікальності.
 * @param {Date} date Об'єкт дати.
 * @param {number} days Кількість днів для додавання.
 * @return {Date} Новий об'єкт дати.
 */
function rmpdTr_addDays(date, days) {
  // Додаємо перевірку, чи 'date' є валідним об'єктом Date
  if (!(date instanceof Date) || isNaN(date.getTime())) {
      Logger.log(`Некоректний вхідний об'єкт дати в rmpdTr_addDays: ${date}`);
      // Повертаємо null або поточну дату, або генеруємо помилку, залежно від бажаної логіки
      return new Date(); // Повертаємо поточну дату як запасний варіант
  }
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

/**
 * Допоміжна функція для форматування дати у форматі yyyy-MM-dd+01:00.
 * Назва функції змінена для унікальності.
 * @param {Date|string|number} dateInput Об'єкт дати, рядок або число (мілісекунди).
 * @return {string} Дата у форматі "yyyy-MM-dd+01:00" або порожній рядок при помилці.
 */
function rmpdTr_formatDateForXml(dateInput) {
   let dateObject;
   // Спроба створити об'єкт Date з різних типів вхідних даних
   if (dateInput instanceof Date && !isNaN(dateInput.getTime())) {
       dateObject = dateInput;
   } else if (typeof dateInput === 'string' || typeof dateInput === 'number') {
       dateObject = new Date(dateInput);
   }

   // Перевірка, чи вдалося створити валідний об'єкт Date
   if (!dateObject || !(dateObject instanceof Date) || isNaN(dateObject.getTime())) {
     Logger.log(`Некоректний вхідний об'єкт дати для форматування в rmpdTr_formatDateForXml: ${dateInput}. Тип: ${typeof dateInput}`);
     return ''; // Повертаємо порожній рядок для некоректних дат
   }
   try {
       // Форматуємо дату у форматі yyyy-MM-dd та додаємо часовий пояс +01:00
       const formattedDate = Utilities.formatDate(dateObject, Session.getScriptTimeZone(), "yyyy-MM-dd");
       return formattedDate + "+01:00";
   } catch (e) {
       Logger.log(`Помилка форматування дати Utilities.formatDate в rmpdTr_formatDateForXml: ${e}. Дата: ${dateObject}`);
       return '';
   }
}
